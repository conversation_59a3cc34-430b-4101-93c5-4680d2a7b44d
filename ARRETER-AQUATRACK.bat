@echo off
title AquaTrack - Arret Application
color 0C

echo.
echo ========================================
echo    🛑 ARRET APPLICATION AQUATRACK
echo ========================================
echo.

echo 🔍 Recherche des processus AquaTrack...

REM Arrêter les processus Node.js liés à AquaTrack
echo 🛑 Arret du serveur backend...
taskkill /f /im node.exe 2>nul
if %errorlevel% equ 0 (
    echo ✅ Serveur backend arrete
) else (
    echo ℹ️  Aucun serveur backend en cours
)

REM Arrêter les processus npm
echo 🛑 Arret du serveur frontend...
taskkill /f /im npm.exe 2>nul
if %errorlevel% equ 0 (
    echo ✅ Serveur frontend arrete
) else (
    echo ℹ️  Aucun serveur frontend en cours
)

REM Arrêter les processus cmd liés
echo 🛑 Fermeture des fenetres de commande...
taskkill /f /fi "WINDOWTITLE eq *AquaTrack*" 2>nul

echo.
echo ✅ APPLICATION AQUATRACK ARRETEE !
echo.
echo 📋 Tous les serveurs ont ete arretes:
echo    🖥️  Backend Node.js (port 4000)
echo    📱 Frontend React (port 3000)
echo.

timeout /t 3 /nobreak >nul
echo 🎯 Vous pouvez maintenant fermer cette fenetre
echo.
pause
