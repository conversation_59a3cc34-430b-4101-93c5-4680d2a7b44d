import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuration de l'API
// Pour React Native sur émulateur/web: localhost fonctionne
// Pour React Native sur téléphone réel: utilisez l'IP de votre PC
const API_BASE_URL = 'http://localhost:4000';

const AuthenticationMobile = ({ navigation }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  // Vérifier si l'utilisateur est déjà connecté au démarrage
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsInitializing(true);
      const userData = await AsyncStorage.getItem('userData');
      const authToken = await AsyncStorage.getItem('authToken');
      
      if (userData && authToken) {
        const user = JSON.parse(userData);
        console.log('👤 Utilisateur déjà connecté:', user.nom, user.prenom, '- Rôle:', user.role);
        
        // Vérifier si le token est encore valide (optionnel)
        const loginTime = await AsyncStorage.getItem('loginTime');
        if (loginTime) {
          const timeDiff = Date.now() - new Date(loginTime).getTime();
          const hoursDiff = timeDiff / (1000 * 60 * 60);
          
          // Si connecté depuis moins de 24h, rediriger automatiquement
          if (hoursDiff < 24) {
            redirectUser(user);
            return;
          }
        }
      }
    } catch (error) {
      console.log('❌ Erreur lors de la vérification du statut d\'authentification:', error);
    } finally {
      setIsInitializing(false);
    }
  };

  const redirectUser = (user) => {
    if (user.role === 'Tech') {
      console.log('🔧 Redirection vers TechnicianDashboard');
      navigation.replace('TechnicianDashboard', { user });
    } else if (user.role === 'Admin') {
      console.log('👨‍💼 Redirection vers Dashboard');
      navigation.replace('Dashboard', { user });
    } else {
      console.log('❓ Rôle non reconnu, redirection vers Dashboard par défaut');
      navigation.replace('Dashboard', { user });
    }
  };

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez saisir votre email et mot de passe');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion:', { email });

      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          password: password
        }),
      });

      const data = await response.json();
      console.log('📥 Réponse du serveur:', data);

      if (data.success) {
        // Sauvegarder les données utilisateur localement
        await AsyncStorage.setItem('userData', JSON.stringify(data.user));
        await AsyncStorage.setItem('authToken', data.token);
        await AsyncStorage.setItem('loginTime', new Date().toISOString());

        Alert.alert(
          'Connexion réussie',
          `Bienvenue ${data.user.prenom} ${data.user.nom}!\nRôle: ${data.user.role}`,
          [
            {
              text: 'OK',
              onPress: () => {
                console.log(`✅ Connexion réussie - Redirection: ${data.redirectTo}`);
                redirectUser(data.user);
              }
            }
          ]
        );
      } else {
        Alert.alert('Erreur de connexion', data.message || 'Erreur de connexion');
      }
    } catch (error) {
      console.error('❌ Erreur de connexion:', error);
      Alert.alert(
        'Erreur de connexion',
        'Impossible de se connecter au serveur. Vérifiez votre connexion internet et que le serveur est démarré sur le port 4000.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await AsyncStorage.multiRemove(['userData', 'authToken', 'loginTime']);
      console.log('👋 Déconnexion réussie');
      // Recharger la page de connexion
      setEmail('');
      setPassword('');
    } catch (error) {
      console.error('❌ Erreur lors de la déconnexion:', error);
    }
  };

  // Écran de chargement initial
  if (isInitializing) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Vérification de la session...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          {/* Logo et titre */}
          <View style={styles.logoContainer}>
            <View style={styles.logoCircle}>
              <Ionicons name="water" size={60} color="#2196F3" />
            </View>
            <Text style={styles.logoTitle}>AquaTrack</Text>
            <Text style={styles.logoSubtitle}>Système de Facturation Mobile</Text>
          </View>

          {/* Formulaire de connexion */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Connexion</Text>
            
            <View style={styles.inputContainer}>
              <Ionicons name="mail-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
            </View>

            <View style={styles.inputContainer}>
              <Ionicons name="lock-closed-outline" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Mot de passe"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
              <TouchableOpacity
                style={styles.eyeIcon}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? "eye-outline" : "eye-off-outline"}
                  size={20}
                  color="#666"
                />
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.loginButton, loading && styles.loginButtonDisabled]}
              onPress={handleLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color="#fff" size="small" />
              ) : (
                <Text style={styles.loginButtonText}>Se connecter</Text>
              )}
            </TouchableOpacity>

            {/* Informations de test */}
            <View style={styles.testInfoContainer}>
              <Text style={styles.testInfoTitle}>Comptes de test :</Text>
              <Text style={styles.testInfoText}>Admin: <EMAIL> / Admin123</Text>
              <Text style={styles.testInfoText}>Tech: <EMAIL> / Tech123</Text>
              <Text style={styles.testInfoNote}>
                Admin → Dashboard | Tech → TechnicianDashboard
              </Text>
            </View>

            {/* Bouton de déconnexion (si nécessaire) */}
            <TouchableOpacity
              style={styles.logoutButton}
              onPress={handleLogout}
            >
              <Text style={styles.logoutButtonText}>Effacer la session</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 20,
    fontSize: 16,
    color: '#666',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  logoTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 8,
  },
  logoSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 30,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    marginBottom: 20,
    paddingHorizontal: 15,
    backgroundColor: '#f9f9f9',
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: '#333',
  },
  eyeIcon: {
    padding: 5,
  },
  loginButton: {
    backgroundColor: '#2196F3',
    borderRadius: 10,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  loginButtonDisabled: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  testInfoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
    marginBottom: 15,
  },
  testInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 5,
  },
  testInfoText: {
    fontSize: 12,
    color: '#1976d2',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  testInfoNote: {
    fontSize: 11,
    color: '#1976d2',
    fontStyle: 'italic',
    marginTop: 5,
  },
  logoutButton: {
    backgroundColor: '#f44336',
    borderRadius: 10,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoutButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default AuthenticationMobile;
