# 🚀 Démarrage Rapide - AquaTrack Mobile sur PC

## ⚡ Méthode la Plus Simple

### 1. **Démarrage Automatique**
```bash
# Double-cliquez sur ce fichier
start-simple.bat
```

### 2. **Démarrage Manuel**
```bash
# Terminal 1 - Backend
node simple-server.js

# Terminal 2 - Mobile (dans le dossier mobile)
cd mobile
npx expo start
```

---

## 📱 Options d'Affichage

### Option 1: **Navigateur Web** (Recommandé)
1. Lancez `start-simple.bat`
2. Le serveur démarre sur http://localhost:4000
3. Testez l'API directement dans le navigateur

### Option 2: **Expo Web**
```bash
cd mobile
npx expo start --web
```
- S'ouvre automatiquement dans le navigateur
- URL: http://localhost:19006

### Option 3: **Expo Go (Téléphone)**
```bash
cd mobile
npx expo start
```
- Scannez le QR code avec Expo Go
- Application native sur votre téléphone

### Option 4: **Émulateur Android**
```bash
cd mobile
npx expo start --android
```
- Nécessite Android Studio installé
- Émulateur Android automatique

---

## 🔧 Si Expo ne Fonctionne Pas

### Installation des Dépendances
```bash
cd mobile
npm install
npx expo install
```

### Réinstallation Expo CLI
```bash
npm uninstall -g expo-cli
npm install -g @expo/cli
```

### Mode Web Manquant
```bash
cd mobile
npx expo install @expo/webpack-config
```

---

## 🌐 Test de l'API Backend

Une fois le serveur démarré, testez ces URLs :

### Test Serveur
```
GET http://localhost:4000/
```

### Test Authentification
```
POST http://localhost:4000/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Tech123"
}
```

### Test Clients
```
GET http://localhost:4000/api/clients
```

---

## 🔑 Comptes de Test

```
Technicien:
- Email: <EMAIL>
- Password: Tech123

Admin:
- Email: <EMAIL>  
- Password: Admin123
```

---

## 📋 Vérification Rapide

✅ **Serveur Backend**
- [ ] Node.js installé
- [ ] Port 4000 libre
- [ ] `simple-server.js` existe

✅ **Application Mobile**
- [ ] Dossier `mobile/` existe
- [ ] `package.json` présent
- [ ] Expo CLI installé

✅ **Configuration**
- [ ] `config.js` avec la bonne IP
- [ ] Même réseau WiFi (PC/téléphone)

---

## 🆘 Problèmes Courants

### "Port 4000 already in use"
```bash
# Tuer le processus sur le port 4000
netstat -ano | findstr :4000
taskkill /PID [PID_NUMBER] /F
```

### "Expo command not found"
```bash
npm install -g @expo/cli
```

### "Module not found"
```bash
cd mobile
npm install
```

### "Network request failed"
- Vérifiez l'IP dans `config.js`
- Assurez-vous que le serveur backend fonctionne

---

## 🎯 Ordre de Démarrage Recommandé

1. **Backend** : `node simple-server.js`
2. **Test** : Ouvrir http://localhost:4000
3. **Mobile** : `cd mobile && npx expo start --web`
4. **Connexion** : Utiliser les comptes de test

---

## 📞 Support

Si rien ne fonctionne :
1. Redémarrez votre PC
2. Vérifiez que Node.js est installé : `node --version`
3. Réinstallez les dépendances : `npm install`
4. Consultez les logs d'erreur dans les terminaux
