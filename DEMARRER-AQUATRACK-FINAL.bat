@echo off
title AquaTrack - Demarrage Final
color 0A

echo.
echo ========================================
echo    💧 AQUATRACK - DEMARRAGE FINAL 💧
echo ========================================
echo.

echo 🛑 1. ARRET DE TOUS LES PROCESSUS...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. ATTENTE DE LIBERATION DES PORTS...
timeout /t 3 /nobreak >nul

echo.
echo 🗄️  3. TEST DE LA BASE DE DONNEES...
node test-auth.js >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PROBLEME BASE DE DONNEES !
    echo.
    echo 🔧 SOLUTIONS:
    echo    1. Demarrez PostgreSQL
    echo    2. Creez la base 'Facturation'
    echo    3. Verifiez les identifiants postgres/123456
    echo.
    pause
    exit /b 1
)
echo ✅ Base de donnees OK

echo.
echo 🖥️  4. DEMARRAGE BACKEND OPTIMISE (Port 4000)...
start "🖥️ Backend AquaTrack" cmd /k "title Backend AquaTrack && color 0B && echo ========================================== && echo    🖥️ SERVEUR BACKEND AQUATRACK && echo ========================================== && echo ✅ Port: 4000 && echo ✅ Base: PostgreSQL Facturation && echo ✅ API: http://localhost:4000 && echo. && echo 🚀 Demarrage du serveur optimise... && echo. && node backend/server-simple.js"

echo ⏳ Attente du demarrage backend...
:wait_backend
timeout /t 2 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 3 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 Backend en cours de demarrage...
    goto wait_backend
)
echo ✅ Backend demarre avec succes !

echo.
echo 🔐 5. TEST DE L'AUTHENTIFICATION...
powershell -Command "try { $body = '{\"email\":\"<EMAIL>\",\"password\":\"Tech123\"}'; $response = Invoke-RestMethod -Uri 'http://localhost:4000/api/auth/login' -Method POST -ContentType 'application/json' -Body $body; if($response.success) { Write-Host 'Authentification: OK' } else { Write-Host 'Authentification: ERREUR' } } catch { Write-Host 'Authentification: ERREUR' }"

echo.
echo 📱 6. DEMARRAGE FRONTEND (Port 3000)...
start "📱 Frontend AquaTrack" cmd /k "title Frontend AquaTrack && color 0E && echo ========================================== && echo    📱 SERVEUR FRONTEND AQUATRACK && echo ========================================== && echo ✅ Port: 3000 && echo ✅ Type: React Native Web && echo ✅ URL: http://localhost:3000 && echo. && echo 🚀 Demarrage React... && echo ⏳ Compilation en cours (peut prendre 1-2 minutes)... && echo. && npm start"

echo ⏳ Attente du demarrage frontend...
set /a counter=0
:wait_frontend
timeout /t 5 /nobreak >nul
set /a counter+=1
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    if %counter% lss 24 (
        echo 🔄 Frontend en cours de demarrage... (%counter%/24 - ~2 minutes max)
        goto wait_frontend
    ) else (
        echo ⚠️  Frontend prend plus de temps que prevu
        echo    L'application devrait etre accessible bientot
    )
) else (
    echo ✅ Frontend demarre avec succes !
)

echo.
echo 🔍 7. VERIFICATION FINALE...
echo 🖥️  Backend:
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 5; Write-Host '   ✅ Status:' $r.StatusCode '- ACCESSIBLE' } catch { Write-Host '   ❌ NON ACCESSIBLE' }"

echo 📱 Frontend:
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5; Write-Host '   ✅ Status:' $r.StatusCode '- ACCESSIBLE' } catch { Write-Host '   ❌ NON ACCESSIBLE (normal si encore en compilation)' }"

echo.
echo 🌐 8. OUVERTURE DE L'APPLICATION...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ AQUATRACK DEMARRE AVEC SUCCES !
echo.
echo 📱 Application Web: http://localhost:3000
echo 🖥️  API Backend: http://localhost:4000
echo.
echo 📋 INFORMATIONS DE CONNEXION:
echo    📧 Email: <EMAIL>
echo    🔐 Mot de passe: Tech123
echo.
echo 🎯 FONCTIONNALITES DISPONIBLES:
echo    👥 Gestion des clients
echo    💧 Saisie des consommations
echo    🗺️  Carte Google Maps par secteur
echo    🧾 Consultation des factures
echo    📱 Scanner QR codes
echo.
echo ⚠️  IMPORTANT:
echo    - Si la page est blanche, attendez 1-2 minutes
echo    - Actualisez la page (F5) si necessaire
echo    - Les logs sont dans les fenetres Backend/Frontend
echo.
echo 🛑 POUR ARRETER:
echo    - Fermez les fenetres Backend et Frontend
echo    - Ou utilisez Ctrl+C dans chaque fenetre
echo.

pause
