@echo off
title AquaTrack - Demarrage Application
color 0A

echo.
echo ========================================
echo    💧 AQUATRACK - FACTURATION EAU 💧
echo ========================================
echo.
echo 🚀 Demarrage de l'application mobile...
echo.

REM Vérifier si Node.js est installé
echo 🔍 Verification de Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installe !
    echo    Telechargez-le depuis: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js detecte: %NODE_VERSION%

REM Vérifier si npm est installé
echo 🔍 Verification de npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm n'est pas installe !
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm detecte: %NPM_VERSION%
echo.

echo 📦 Verification des dependances...
if not exist node_modules (
    echo 🔄 Installation des modules npm...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Erreur lors de l'installation !
        pause
        exit /b 1
    )
    echo ✅ Modules installes avec succes !
) else (
    echo ✅ Modules npm deja installes
)
echo.

echo 🔧 Configuration:
echo    📱 Frontend React Native: http://localhost:3000
echo    🖥️  Backend Node.js: http://localhost:4000
echo    🗄️  Base de donnees: PostgreSQL (Facturation)
echo.

echo 🚀 Demarrage du serveur BACKEND (port 4000)...
start "🖥️ AquaTrack Backend" cmd /k "echo 🖥️ SERVEUR BACKEND AQUATRACK && echo. && echo 📡 Port: 4000 && echo 🗄️ Base: PostgreSQL && echo. && node backend/server-db.js"

echo ⏳ Attente du demarrage du backend...
timeout /t 5 /nobreak >nul

echo 🌐 Demarrage du serveur FRONTEND (port 3000)...
start "📱 AquaTrack Frontend" cmd /k "echo 📱 FRONTEND AQUATRACK && echo. && echo 🌐 Port: 3000 && echo 📱 Type: React Native Web && echo. && npm start"

echo.
echo ✅ APPLICATION EN COURS DE DEMARRAGE...
echo.
echo 📱 L'application sera disponible dans votre navigateur:
echo    👉 http://localhost:3000
echo.
echo 🔧 API Backend disponible sur:
echo    👉 http://localhost:4000
echo.
echo 📋 INFORMATIONS DE CONNEXION:
echo    📧 Email: <EMAIL>
echo    🔐 Mot de passe: Tech123
echo.
echo ⚠️  IMPORTANT:
echo    ✅ PostgreSQL doit etre demarre
echo    ✅ Base de donnees 'Facturation' doit exister
echo    ✅ Utilisateur: postgres, Mot de passe: 123456
echo.
echo 🛑 Pour arreter l'application:
echo    - Fermez les deux fenetres de commande
echo    - Ou appuyez sur Ctrl+C dans chaque fenetre
echo.
echo 🎯 L'application va s'ouvrir automatiquement...

REM Attendre que les serveurs démarrent
timeout /t 8 /nobreak >nul

REM Ouvrir l'application dans le navigateur
echo 🌐 Ouverture de l'application...
start http://localhost:3000

echo.
echo 🎉 APPLICATION DEMARREE AVEC SUCCES !
echo.
echo 📞 En cas de probleme:
echo    1. Verifiez que PostgreSQL est demarre
echo    2. Verifiez la base de donnees 'Facturation'
echo    3. Redemarrez ce script
echo.

pause
