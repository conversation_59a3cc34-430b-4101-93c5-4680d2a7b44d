@echo off
title AquaTrack - Demarrage Serveurs
color 0A

echo.
echo ========================================
echo    🚀 DEMARRAGE SERVEURS AQUATRACK
echo ========================================
echo.

echo 🛑 Arret des processus existants...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ Attente de liberation des ports (5 secondes)...
timeout /t 5 /nobreak >nul

echo.
echo 🗄️  Test de la base de donnees...
node test-auth.js >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Probleme avec la base de donnees !
    echo    Verifiez que PostgreSQL est demarre
    echo    et que la base 'Facturation' existe
    pause
    exit /b 1
)
echo ✅ Base de donnees OK

echo.
echo 🖥️  DEMARRAGE DU SERVEUR BACKEND (Port 4000)...
start "🖥️ Backend AquaTrack" cmd /k "echo ========================================== && echo    🖥️ SERVEUR BACKEND AQUATRACK && echo ========================================== && echo Port: 4000 && echo Base: PostgreSQL Facturation && echo API: http://localhost:4000 && echo. && echo 🚀 Demarrage en cours... && echo. && node backend/server-db.js"

echo ⏳ Attente du demarrage du backend...
:wait_backend
timeout /t 2 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 3 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 Backend en cours de demarrage...
    goto wait_backend
)
echo ✅ Backend demarre avec succes !

echo.
echo 🔐 Test de l'API d'authentification...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:4000/api/auth/users' -TimeoutSec 5 | Out-Null; Write-Host 'API Auth: OK' } catch { Write-Host 'API Auth: ERREUR' }"

echo.
echo 📱 DEMARRAGE DU SERVEUR FRONTEND (Port 3000)...
start "📱 Frontend AquaTrack" cmd /k "echo ========================================== && echo    📱 SERVEUR FRONTEND AQUATRACK && echo ========================================== && echo Port: 3000 && echo Type: React Native Web && echo URL: http://localhost:3000 && echo. && echo 🚀 Demarrage en cours... && echo ⏳ Compilation React en cours... && echo. && npm start"

echo ⏳ Attente du demarrage du frontend (peut prendre 30-60 secondes)...
set /a counter=0
:wait_frontend
timeout /t 3 /nobreak >nul
set /a counter+=1
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 3 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    if %counter% lss 20 (
        echo 🔄 Frontend en cours de demarrage... (%counter%/20)
        goto wait_frontend
    ) else (
        echo ⚠️  Frontend prend plus de temps que prevu
        echo    Continuez quand meme...
    )
) else (
    echo ✅ Frontend demarre avec succes !
)

echo.
echo 🔍 Verification finale des serveurs...
echo 🖥️  Backend (port 4000):
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 5; Write-Host '   ✅ Status:' $r.StatusCode } catch { Write-Host '   ❌ ERREUR:' $_.Exception.Message }"

echo 📱 Frontend (port 3000):
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5; Write-Host '   ✅ Status:' $r.StatusCode } catch { Write-Host '   ❌ ERREUR:' $_.Exception.Message }"

echo.
echo 🌐 Ouverture de l'application...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ SERVEURS DEMARRES AVEC SUCCES !
echo.
echo 📱 Application Web: http://localhost:3000
echo 🖥️  API Backend: http://localhost:4000
echo.
echo 📋 INFORMATIONS DE CONNEXION:
echo    📧 Email: <EMAIL>
echo    🔐 Mot de passe: Tech123
echo.
echo 🎯 Si vous voyez encore l'erreur d'authentification:
echo    1. Attendez 30 secondes que React se charge completement
echo    2. Actualisez la page (F5)
echo    3. Verifiez les fenetres Backend et Frontend pour les erreurs
echo.
echo 🛑 Pour arreter les serveurs:
echo    - Fermez les fenetres "Backend AquaTrack" et "Frontend AquaTrack"
echo    - Ou utilisez Ctrl+C dans chaque fenetre
echo.

pause
