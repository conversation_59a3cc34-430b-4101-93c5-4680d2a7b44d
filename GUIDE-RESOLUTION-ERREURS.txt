========================================
🔧 GUIDE DE RÉSOLUTION DES ERREURS
========================================

❌ ERREUR: "Impossible de se connecter au serveur"
🔧 SOLUTION:
   1. Double-cliquez sur: DEMARRER-AQUATRACK-FINAL.bat
   2. Attendez que les deux serveurs démarrent
   3. Ouvrez http://localhost:3000

❌ ERREUR: "Erreur serveur lors de l'authentification"
🔧 SOLUTION:
   ✅ RÉSOLU ! Utilisez le serveur optimisé
   1. Utilisez: DEMARRER-AQUATRACK-FINAL.bat
   2. Email: <EMAIL>
   3. Mot de passe: Tech123

❌ ERREUR: "Page blanche dans le navigateur"
🔧 SOLUTION:
   1. Attendez 1-2 minutes (compilation React)
   2. Actualisez la page (F5)
   3. Vérifiez que le frontend démarre dans sa fenêtre

❌ ERREUR: "Port déjà utilisé"
🔧 SOLUTION:
   1. Fermez toutes les fenêtres de commande
   2. Attendez 10 secondes
   3. Relancez DEMARRER-AQUATRACK-FINAL.bat

❌ ERREUR: "Base de données introuvable"
🔧 SOLUTION:
   1. Démarrez PostgreSQL
   2. Créez la base "Facturation"
   3. Utilisateur: postgres / Mot de passe: 123456

========================================
📋 ORDRE DE DÉMARRAGE CORRECT
========================================

1️⃣ ARRÊTER tous les processus existants
2️⃣ TESTER la base de données
3️⃣ DÉMARRER le backend (port 4000)
4️⃣ ATTENDRE que le backend soit accessible
5️⃣ TESTER l'authentification
6️⃣ DÉMARRER le frontend (port 3000)
7️⃣ ATTENDRE la compilation React
8️⃣ OUVRIR http://localhost:3000

========================================
🎯 SCRIPTS DISPONIBLES
========================================

🚀 DEMARRER-AQUATRACK-FINAL.bat
   → Script principal optimisé (RECOMMANDÉ)

🔍 test-authentification.js
   → Teste l'API d'authentification

🗄️ test-auth.js
   → Teste la base de données

🛑 ARRETER-AQUATRACK.bat
   → Arrête tous les serveurs

========================================
📱 INFORMATIONS DE CONNEXION
========================================

🌐 Application: http://localhost:3000
🖥️  API Backend: http://localhost:4000

👤 COMPTE TECHNICIEN:
   📧 Email: <EMAIL>
   🔐 Mot de passe: Tech123

👤 COMPTE ADMIN:
   📧 Email: <EMAIL>
   🔐 Mot de passe: Admin123

========================================
🎯 FONCTIONNALITÉS PRINCIPALES
========================================

✅ Authentification sécurisée
✅ Dashboard technicien
✅ Gestion des clients
✅ Saisie des consommations
✅ Carte Google Maps par secteur
✅ Consultation des factures
✅ Scanner QR codes
✅ Historique des actions

========================================
🔧 SERVEUR BACKEND OPTIMISÉ
========================================

Le nouveau serveur (server-simple.js) corrige:
✅ Erreurs d'authentification
✅ Logs détaillés pour le debug
✅ Gestion d'erreurs améliorée
✅ Tests automatiques intégrés

========================================
📞 EN CAS DE PROBLÈME PERSISTANT
========================================

1. Vérifiez les logs dans les fenêtres:
   - "Backend AquaTrack" (serveur)
   - "Frontend AquaTrack" (React)

2. Testez manuellement:
   - http://localhost:4000 (backend)
   - http://localhost:3000 (frontend)

3. Redémarrez complètement:
   - Fermez toutes les fenêtres
   - Relancez DEMARRER-AQUATRACK-FINAL.bat

========================================
🎉 RÉSOLUTION TERMINÉE !
========================================

L'erreur d'authentification est maintenant résolue.
Votre application AquaTrack fonctionne correctement !
