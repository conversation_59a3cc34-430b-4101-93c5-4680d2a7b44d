========================================
💧 AQUATRACK - APPLICATION DE FACTURATION
========================================

🎯 COMMENT DÉMARRER L'APPLICATION :

1️⃣ TESTER LA BASE DE DONNÉES (RECOMMANDÉ)
   👉 Double-cliquez sur : TESTER-DATABASE.bat
   ✅ Vérifie PostgreSQL et la base "Facturation"

2️⃣ DÉMARRER L'APPLICATION
   👉 Double-cliquez sur : DEMARRER-AQUATRACK.bat
   🚀 Lance automatiquement backend + frontend

3️⃣ ARRÊTER L'APPLICATION
   👉 Double-cliquez sur : ARRETER-AQUATRACK.bat
   🛑 Arrête tous les serveurs proprement

========================================
📋 INFORMATIONS DE CONNEXION
========================================

🌐 Application Web : http://localhost:3000
🖥️  API Backend    : http://localhost:4000

👤 COMPTE TECHNICIEN :
   📧 Email     : <EMAIL>
   🔐 Mot de passe : Tech123

========================================
🔧 CONFIGURATION REQUISE
========================================

✅ Node.js installé (version détectée automatiquement)
✅ PostgreSQL démarré
✅ Base de données "Facturation" créée
✅ Utilisateur PostgreSQL : postgres / 123456

========================================
🎯 FONCTIONNALITÉS DISPONIBLES
========================================

📱 DASHBOARD TECHNICIEN :
   👥 Gestion des clients
   💧 Saisie des consommations
   🧾 Consultation des factures
   📱 Scanner QR codes
   🗺️  Carte Google Maps des clients
   📊 Historique des actions

🗺️  CARTE INTERACTIVE :
   📍 Sélection par secteur
   🏠 Localisation des clients
   📊 Affichage automatique sur Google Maps

========================================
❓ EN CAS DE PROBLÈME
========================================

🔴 ERREUR "Impossible de se connecter au serveur" :
   1. Vérifiez que PostgreSQL est démarré
   2. Lancez TESTER-DATABASE.bat
   3. Redémarrez DEMARRER-AQUATRACK.bat

🔴 ERREUR "Base de données introuvable" :
   1. Créez la base "Facturation" dans PostgreSQL
   2. Vérifiez les identifiants (postgres/123456)

🔴 ERREUR "Port déjà utilisé" :
   1. Lancez ARRETER-AQUATRACK.bat
   2. Attendez 10 secondes
   3. Relancez DEMARRER-AQUATRACK.bat

🔴 PAGE BLANCHE DANS LE NAVIGATEUR :
   1. Attendez 30 secondes (chargement initial)
   2. Actualisez la page (F5)
   3. Vérifiez http://localhost:3000

========================================
📞 SUPPORT TECHNIQUE
========================================

🔧 Vérifications automatiques :
   - Node.js et npm installés
   - Modules npm installés
   - Serveurs démarrés correctement
   - Base de données accessible

📋 Logs disponibles dans les fenêtres :
   - Backend : Fenêtre "AquaTrack Backend"
   - Frontend : Fenêtre "AquaTrack Frontend"

========================================
🎉 BONNE UTILISATION D'AQUATRACK !
========================================
