@echo off
title AquaTrack - Resolution Probleme Connexion
color 0E

echo.
echo ========================================
echo    🔧 RESOLUTION PROBLEME CONNEXION
echo ========================================
echo.

echo 🛑 Etape 1: Arret de tous les processus...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ Attente de liberation des ports...
timeout /t 3 /nobreak >nul

echo.
echo 🔍 Etape 2: Verification des ports...
netstat -an | findstr ":3000\|:4000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  Ports encore occupes, nouvelle tentative...
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Ports liberes
)

echo.
echo 🗄️  Etape 3: Test de la base de donnees...
node test-auth.js >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Base de donnees OK
) else (
    echo ❌ Probleme base de donnees - Verifiez PostgreSQL
    pause
    exit /b 1
)

echo.
echo 🖥️  Etape 4: Demarrage du BACKEND (port 4000)...
start "Backend AquaTrack" cmd /k "echo 🖥️ BACKEND AQUATRACK && echo Port: 4000 && echo. && node backend/server-db.js"

echo ⏳ Attente du demarrage backend...
:wait_backend
timeout /t 2 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:4000' -TimeoutSec 2 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 Backend en cours de demarrage...
    goto wait_backend
)
echo ✅ Backend demarre avec succes !

echo.
echo 📱 Etape 5: Demarrage du FRONTEND (port 3000)...
start "Frontend AquaTrack" cmd /k "echo 📱 FRONTEND AQUATRACK && echo Port: 3000 && echo. && npm start"

echo ⏳ Attente du demarrage frontend...
:wait_frontend
timeout /t 3 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 2 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo 🔄 Frontend en cours de demarrage...
    goto wait_frontend
)
echo ✅ Frontend demarre avec succes !

echo.
echo 🔍 Etape 6: Test final de connexion...
powershell -Command "try { $r = Invoke-WebRequest -Uri 'http://localhost:4000/api/test-db' -TimeoutSec 5; Write-Host 'API Backend:' $r.StatusCode } catch { Write-Host 'API Backend: ERREUR' }"

echo.
echo 🌐 Etape 7: Ouverture de l'application...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo ✅ RESOLUTION TERMINEE !
echo.
echo 📱 Application disponible sur: http://localhost:3000
echo 🖥️  API Backend sur: http://localhost:4000
echo.
echo 📋 INFORMATIONS DE CONNEXION:
echo    📧 Email: <EMAIL>
echo    🔐 Mot de passe: Tech123
echo.
echo 🔧 Si le probleme persiste:
echo    1. Verifiez que PostgreSQL est demarre
echo    2. Verifiez la base 'Facturation'
echo    3. Redemarrez ce script
echo.

pause
