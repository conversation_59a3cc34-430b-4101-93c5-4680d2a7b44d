@echo off
title Solution Rapide - AquaTrack
color 0A

echo.
echo ========================================
echo    🔧 SOLUTION RAPIDE AQUATRACK
echo ========================================
echo.

echo 🛑 1. ARRET DE TOUS LES PROCESSUS...
taskkill /f /im node.exe 2>nul
taskkill /f /im npm.exe 2>nul
echo ✅ Processus arretes

echo.
echo ⏳ 2. ATTENTE (5 secondes)...
timeout /t 5 /nobreak >nul

echo.
echo 🖥️  3. DEMARRAGE BACKEND...
start "Backend" cmd /k "echo BACKEND AQUATRACK - Port 4000 && node backend/server-db.js"

echo ⏳ Attente backend (8 secondes)...
timeout /t 8 /nobreak >nul

echo.
echo 📱 4. DEMARRAGE FRONTEND...
start "Frontend" cmd /k "echo FRONTEND AQUATRACK - Port 3000 && npm start"

echo ⏳ Attente frontend (15 secondes)...
timeout /t 15 /nobreak >nul

echo.
echo 🌐 5. OUVERTURE APPLICATION...
start http://localhost:3000

echo.
echo ✅ SOLUTION APPLIQUEE !
echo.
echo 📱 Application: http://localhost:3000
echo 📧 Email: <EMAIL>
echo 🔐 Mot de passe: Tech123
echo.

pause
