@echo off
echo ========================================
echo    DEMARRAGE AQUATRACK COMPLET
echo    AVEC CHAMP CONTRAT AMELIORE
echo ========================================

echo.
echo 1. Arret des anciens processus Node.js...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Verification de PostgreSQL...
echo Assurez-vous que PostgreSQL est demarre
echo Base de donnees: Facturation

echo.
echo 3. Attente de 2 secondes...
timeout /t 2 /nobreak > nul

echo.
echo 4. Demarrage du serveur backend avec base de donnees...
cd backend
start "Backend AquaTrack DB" cmd /k "echo Serveur backend avec PostgreSQL demarre... && node server-db.js"

echo.
echo 5. Attente de 5 secondes pour le demarrage...
timeout /t 5 /nobreak > nul

echo.
echo 6. Test des APIs...
echo Test API clients...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients'; Write-Host '✅ API Clients OK:' $r.total 'clients' } catch { Write-Host '❌ Erreur API Clients' }"

echo Test API contrats (client ID 15)...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients/15/contracts'; Write-Host '✅ API Contrats OK:' $r.count 'contrats' } catch { Write-Host '❌ Erreur API Contrats' }"

echo.
echo 7. Ouverture du navigateur pour tester...
start http://localhost:4000/api/clients

echo.
echo 8. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    AQUATRACK COMPLET DEMARRE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📊 API Clients: http://localhost:4000/api/clients
echo 🌐 Frontend React: http://localhost:3000
echo.
echo NOUVELLES FONCTIONNALITES:
echo ✅ Champ contrat intelligent dans le formulaire
echo ✅ Affichage automatique si 1 contrat
echo ✅ Selection multiple si plusieurs contrats
echo ✅ Message si aucun contrat
echo ✅ Authentification corrigee
echo ✅ 22 clients de la base de donnees
echo.
echo INSTRUCTIONS:
echo 1. Le serveur backend est connecte a PostgreSQL
echo 2. L'application React va s'ouvrir automatiquement
echo 3. Connectez-<NAME_EMAIL> / Tech123
echo 4. Testez le formulaire de consommation avec le nouveau champ contrat
echo.
echo COMPTES DE TEST:
echo - Tech: <EMAIL> / Tech123
echo - Admin: <EMAIL> / Admin123
echo.
echo VERIFICATION:
echo - Backend: http://localhost:4000
echo - Clients: http://localhost:4000/api/clients
echo - Contrats: http://localhost:4000/api/clients/[ID]/contracts
echo - Test DB: http://localhost:4000/api/test-db
echo.
echo Appuyez sur une touche pour fermer cette fenetre...
pause > nul
