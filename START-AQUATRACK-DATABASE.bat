@echo off
echo ========================================
echo    DEMARRAGE AQUATRACK AVEC BASE DE DONNEES
echo ========================================

echo.
echo 1. Arret des anciens processus Node.js...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Verification de PostgreSQL...
echo Assurez-vous que PostgreSQL est demarre
echo Base de donnees: Facturation

echo.
echo 3. Attente de 2 secondes...
timeout /t 2 /nobreak > nul

echo.
echo 4. Demarrage du serveur backend avec base de donnees...
cd backend
start "Backend AquaTrack DB" cmd /k "echo Serveur backend avec PostgreSQL demarre... && node server-db.js"

echo.
echo 5. Attente de 5 secondes pour le demarrage...
timeout /t 5 /nobreak > nul

echo.
echo 6. Test de l'API clients...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients'; Write-Host '✅ API Clients OK:' $r.total 'clients trouves' } catch { Write-Host '❌ Erreur API:' $_.Exception.Message }"

echo.
echo 7. Ouverture du navigateur pour tester...
start http://localhost:4000/api/clients

echo.
echo 8. Demarrage de l'application mobile...
cd ..\mobile
start "Mobile AquaTrack" cmd /k "echo Application mobile demarre... && npx expo start"

echo.
echo ========================================
echo    AQUATRACK AVEC BASE DE DONNEES DEMARRE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📊 API Clients: http://localhost:4000/api/clients
echo 📱 Mobile: Expo Dev Tools
echo.
echo INSTRUCTIONS:
echo 1. Le serveur backend est connecte a PostgreSQL
echo 2. L'API clients affiche vos 22 clients de la base
echo 3. Dans Expo, appuyez sur 'w' pour le mode web
echo 4. Testez la <NAME_EMAIL>
echo.
echo COMPTES DE TEST:
echo - Utilisez les comptes de votre table 'utilisateur'
echo - Ou <EMAIL> / Tech123 (si existe)
echo.
echo VERIFICATION:
echo - Backend fonctionne: http://localhost:4000
echo - Clients API: http://localhost:4000/api/clients
echo - Test DB: http://localhost:4000/api/test-db
echo.
echo Si erreur "Impossible de se connecter":
echo 1. Verifiez que PostgreSQL est demarre
echo 2. Verifiez le mot de passe dans server-db.js
echo 3. Verifiez que la base "Facturation" existe
echo.
echo Appuyez sur une touche pour fermer cette fenetre...
pause > nul
