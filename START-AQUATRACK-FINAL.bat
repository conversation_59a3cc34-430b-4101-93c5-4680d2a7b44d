@echo off
echo ========================================
echo    DEMARRAGE AQUATRACK - SOLUTION FINALE
echo ========================================

echo.
echo 1. Arret des anciens processus Node.js...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Attente de 2 secondes...
timeout /t 2 /nobreak > nul

echo.
echo 3. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-minimal.js"

echo.
echo 4. Attente de 5 secondes pour le demarrage...
timeout /t 5 /nobreak > nul

echo.
echo 5. Ouverture du navigateur pour tester...
start http://localhost:4000

echo.
echo 6. Demarrage de l'application mobile...
cd ..\mobile
start "Mobile AquaTrack" cmd /k "echo Application mobile demarre... && npx expo start"

echo.
echo ========================================
echo    AQUATRACK DEMARRE AVEC SUCCES !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📱 Mobile: Expo Dev Tools
echo.
echo INSTRUCTIONS:
echo 1. Le serveur backend est demarre (fenetre separee)
echo 2. L'application mobile Expo va se lancer (fenetre separee)
echo 3. Dans Expo, appuyez sur 'w' pour le mode web
echo 4. Ou scannez le QR code avec Expo Go
echo.
echo COMPTES DE TEST:
echo - Email: <EMAIL>
echo - Password: Tech123
echo.
echo RESOLUTION DES PROBLEMES:
echo - Si "Impossible de se connecter": Verifiez que le backend fonctionne
echo - Si erreur mobile: Changez l'IP dans AuthenticationMobile.js
echo - Si port occupe: Relancez ce script
echo.
echo Appuyez sur une touche pour fermer cette fenetre...
pause > nul
