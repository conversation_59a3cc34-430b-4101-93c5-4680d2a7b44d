@echo off
echo ========================================
echo    AQUATRACK - FORMULAIRE TAILLE REDUITE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    MODIFICATIONS APPLIQUEES !
echo ========================================
echo.
echo 📏 TAILLES REDUITES:
echo ✅ Largeur formulaire: 90%% → 70%%
echo ✅ Padding general: 20px → 15px
echo ✅ Champs input: 16px → 14px
echo ✅ Hauteur input: auto → 40px
echo ✅ Hauteur boutons: auto → 45px
echo ✅ Espacement reduit partout
echo.
echo 📋 SECTION CONTRAT OPTIMISEE:
echo ✅ Taille police reduite
echo ✅ Padding et marges reduites
echo ✅ Hauteur max selecteur: 200px → 150px
echo ✅ Bordures plus fines
echo.
echo 🎯 RESULTAT:
echo ✅ Formulaire plus compact
echo ✅ Meilleure utilisation de l'espace
echo ✅ Interface plus propre
echo ✅ Champ contrat bien integre
echo.
echo 📱 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Observez le formulaire plus compact !
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
