@echo off
echo ========================================
echo    AQUATRACK - BOUTON D'ERREUR PERIODE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    BOUTON D'ERREUR PERIODE ACTIVE !
echo ========================================
echo.
echo 🔴 BOUTON D'ERREUR IMPLEMENTÉ:
echo ✅ Apparait automatiquement si periode invalide
echo ✅ Couleur rouge avec icone d'avertissement
echo ✅ Message explicatif clair
echo ✅ Action de correction automatique
echo.
echo 🎯 FONCTIONNEMENT:
echo 1. Technicien saisit mois actuel/futur
echo 2. Champ devient rouge (bordure d'erreur)
echo 3. Bouton d'erreur apparait immediatement
echo 4. Clic sur bouton → popup explicatif
echo 5. Bouton "Compris" → correction automatique
echo.
echo 🎨 DESIGN DU BOUTON D'ERREUR:
echo ✅ Fond rouge (#dc3545)
echo ✅ Icone: ⚠️ (avertissement)
echo ✅ Texte: "Periode Invalide - Cliquez pour corriger"
echo ✅ Ombre et elevation pour visibilite
echo ✅ Animation tactile
echo.
echo 📋 SCENARIOS DE TEST:
echo.
echo ❌ SAISIR "2025-01" (mois actuel):
echo    → Champ rouge + Bouton d'erreur
echo    → Clic bouton → Popup + correction auto
echo.
echo ❌ SAISIR "2025-02" (mois futur):
echo    → Champ rouge + Bouton d'erreur
echo    → Clic bouton → Popup + correction auto
echo.
echo ✅ SAISIR "2024-12" (mois precedent):
echo    → Champ normal + Pas de bouton d'erreur
echo    → Affichage des mois disponibles
echo.
echo 🚫 BLOCAGES IMPLEMENTES:
echo ✅ Impossible de soumettre avec erreur periode
echo ✅ Message d'alerte si tentative de soumission
echo ✅ Validation en temps reel
echo ✅ Correction automatique proposee
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Dans le champ Periode, tapez "2025-01"
echo 5. OBSERVEZ: Champ rouge + Bouton d'erreur rouge
echo 6. Cliquez sur le bouton d'erreur
echo 7. OBSERVEZ: Popup explicatif + correction auto
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
