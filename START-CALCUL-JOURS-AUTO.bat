@echo off
echo ========================================
echo    AQUATRACK - CALCUL JOURS AUTOMATIQUE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    CALCUL AUTOMATIQUE DES JOURS ACTIVE !
echo ========================================
echo.
echo 🔢 FONCTIONNALITE IMPLEMENTEE:
echo ✅ Calcul automatique du nombre de jours
echo ✅ Base sur la derniere periode de consommation
echo ✅ Compare avec la periode choisie par le technicien
echo ✅ Gestion des formats de periode multiples
echo ✅ Affichage informatif du calcul
echo.
echo 📊 LOGIQUE DE CALCUL:
echo "Nombre de jours = Periode actuelle - Derniere periode"
echo.
echo Exemple:
echo - Derniere consommation: juillet 2025
echo - Periode choisie: 2025-08 (aout 2025)
echo - Calcul: 31 jours (1 mois)
echo.
echo 🎯 FORMATS SUPPORTES:
echo ✅ YYYY-MM (ex: 2024-12)
echo ✅ Mois francais (ex: juillet 2025)
echo ✅ Conversion automatique entre formats
echo ✅ Gestion des annees bissextiles
echo.
echo 🔄 MISE A JOUR AUTOMATIQUE:
echo 1. Client selectionne → Derniere consommation recuperee
echo 2. Contrat change → Nouvelle derniere consommation
echo 3. Periode modifiee → Recalcul automatique
echo 4. Affichage temps reel du nombre de jours
echo.
echo 📋 EXEMPLES DE CALCULS TESTES:
echo ✅ 2024-10 → 2024-11 = 31 jours
echo ✅ 2024-12 → 2025-01 = 31 jours  
echo ✅ juillet 2025 → 2025-08 = 31 jours
echo ✅ 2024-10 → 2024-12 = 61 jours (2 mois)
echo.
echo 🎨 AFFICHAGE AMELIORE:
echo ✅ Champ en lecture seule (non modifiable)
echo ✅ Texte informatif sous le champ
echo ✅ Format: "Calcule entre [periode1] et [periode2]"
echo ✅ Couleur bleue pour l'information
echo.
echo ⚠️ GESTION D'ERREURS:
echo ✅ Periode invalide → Valeur par defaut (30 jours)
echo ✅ Periode anterieure → 0 jours
echo ✅ Difference trop importante → 30 jours
echo ✅ Donnees manquantes → Champ vide
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Observez la "Consommation Precedente" et sa periode
echo 5. Modifiez la "Periode" dans le formulaire
echo 6. OBSERVEZ: Le "Nombre de jours" se calcule automatiquement
echo 7. Changez de contrat → Nouveau calcul automatique
echo.
echo 💡 SCENARIOS DE TEST:
echo.
echo Exemple avec client ayant une consommation en "juillet 2025":
echo - Choisir periode "2025-08" → Calcul: 31 jours
echo - Choisir periode "2025-09" → Calcul: 62 jours
echo - Choisir periode "2025-06" → Calcul: 0 jours (anterieur)
echo.
echo 📊 DONNEES DISPONIBLES:
echo ✅ Consommations avec periodes variees
echo ✅ Formats mixtes (YYYY-MM et mois francais)
echo ✅ Plusieurs clients et contrats
echo ✅ Historique de consommations
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
