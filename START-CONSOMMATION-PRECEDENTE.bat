@echo off
echo ========================================
echo    AQUATRACK - CONSOMMATION PRECEDENTE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Test de l'API consommation...
echo Test API derniere consommation (contrat 10)...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/contracts/10/last-consumption'; Write-Host '✅ API Consommation OK:' $r.lastConsumption.consommationactuelle 'm³' } catch { Write-Host '❌ Erreur API Consommation' }"

echo.
echo 5. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    CONSOMMATION PRECEDENTE IMPLEMENTEE !
echo ========================================
echo.
echo 💧 FONCTIONNALITE AJOUTEE:
echo ✅ Recuperation automatique de la derniere consommation
echo ✅ Affichage dans le champ "Consommation Precedente"
echo ✅ Basee sur la table consommation de la base de donnees
echo ✅ Liee au contrat selectionne (pas au client)
echo.
echo 🔄 LOGIQUE IMPLEMENTEE:
echo 1. Client selectionne → Contrats recuperes
echo 2. Contrat selectionne → Derniere consommation recuperee
echo 3. Affichage automatique dans le formulaire
echo 4. Changement de contrat → Nouvelle consommation
echo.
echo 📊 DONNEES DE TEST DISPONIBLES:
echo ✅ 47 consommations dans la base
echo ✅ Plusieurs periodes (2024-10, 2024-11, 2024-12)
echo ✅ Differents clients et contrats
echo ✅ Historique complet des consommations
echo.
echo 🎯 API CREEE:
echo GET /api/contracts/:id/last-consumption
echo → Retourne la derniere consommation du contrat
echo → Triee par periode DESC puis idcons DESC
echo → Gere les cas sans consommation precedente
echo.
echo 📋 STRUCTURE CONSOMMATION:
echo - idcons: ID unique
echo - consommationpre: Consommation precedente
echo - consommationactuelle: Consommation actuelle
echo - idcont: Reference au contrat
echo - periode: Periode de facturation
echo - status: Statut de la consommation
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Selectionnez un contrat
echo 5. OBSERVEZ: Le champ "Consommation Precedente" se remplit automatiquement
echo 6. Changez de contrat → La consommation change aussi
echo.
echo 💡 EXEMPLES DE CONSOMMATIONS:
echo - Benali Fatima: 280 m³ (periode 2025-08)
echo - Client1: 150 m³ (periode juillet 2025)
echo - Autres clients: Diverses consommations
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
