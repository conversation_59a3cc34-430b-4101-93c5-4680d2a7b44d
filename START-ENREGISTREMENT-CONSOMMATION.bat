@echo off
echo ========================================
echo    AQUATRACK - ENREGISTREMENT CONSOMMATION
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Attente de 3 secondes...
timeout /t 3 /nobreak > nul

echo.
echo 3. Demarrage du serveur backend avec API consommation...
cd backend
start "Backend AquaTrack API" cmd /k "echo Serveur backend avec API consommation demarre... && node server-db.js"

echo.
echo 4. Attente de 8 secondes pour le demarrage complet...
timeout /t 8 /nobreak > nul

echo.
echo 5. Test de l'API d'enregistrement...
echo Test connexion serveur...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/'; Write-Host '✅ Serveur OK' } catch { Write-Host '❌ Serveur KO:' $_.Exception.Message }"

echo.
echo Test API clients...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients'; Write-Host '✅ API Clients OK:' $r.total 'clients' } catch { Write-Host '❌ API Clients KO' }"

echo.
echo 6. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    PROBLEME RESOLU !
echo ========================================
echo.
echo 🔧 ERREUR CORRIGEE:
echo ❌ Avant: "Unexpected token '<', '<!DOCTYPE'... is not valid JSON"
echo ✅ Apres: API d'enregistrement de consommation creee
echo.
echo 📡 NOUVELLE API AJOUTEE:
echo POST /api/consommations
echo → Enregistre une nouvelle consommation dans la base
echo → Retourne une reponse JSON valide
echo → Gere les erreurs proprement
echo.
echo 📊 DONNEES ENVOYEES:
echo {
echo   "periode": "2025-01",
echo   "consommationPre": 150,
echo   "consommationActuelle": 180,
echo   "jours": 31,
echo   "idcont": 10,
echo   "idtech": 1,
echo   "idtranch": 1,
echo   "status": "En cours"
echo }
echo.
echo ✅ VALIDATIONS AJOUTEES:
echo ✅ Verification des donnees requises
echo ✅ Verification de l'existence du contrat
echo ✅ Verification de la selection du contrat
echo ✅ Gestion des erreurs de connexion
echo ✅ Messages d'erreur explicites
echo.
echo 🔄 FLUX CORRIGE:
echo 1. Technicien remplit le formulaire
echo 2. Clic sur "Enregistrer"
echo 3. Validation des donnees
echo 4. Envoi vers POST /api/consommations
echo 5. Insertion en base de donnees
echo 6. Reponse JSON de succes
echo 7. Message de confirmation
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Remplissez le formulaire completement
echo 5. Cliquez sur "Enregistrer"
echo 6. OBSERVEZ: Message "Consommation enregistree avec succes !"
echo.
echo 💡 POINTS IMPORTANTS:
echo ✅ Serveur backend doit etre demarre AVANT React
echo ✅ Attendre 8 secondes pour le demarrage complet
echo ✅ Verifier que le port 4000 est libre
echo ✅ S'assurer que PostgreSQL est demarre
echo.
echo 🔍 DEBUGGING:
echo - Backend: http://localhost:4000
echo - API Test: http://localhost:4000/api/clients
echo - Logs serveur: Voir fenetre "Backend AquaTrack API"
echo - Logs React: Voir fenetre "React AquaTrack"
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
