@echo off
echo ========================================
echo    AQUATRACK - ERREUR CLIENTS RESOLUE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 8 secondes pour le demarrage complet...
timeout /t 8 /nobreak > nul

echo.
echo 4. Test de l'API clients...
echo Test API clients...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/clients'; Write-Host '✅ API Clients OK - Format:' $r.GetType().Name; Write-Host '   Success:' $r.success; Write-Host '   Total:' $r.total; Write-Host '   Data type:' $r.data.GetType().Name } catch { Write-Host '❌ API Clients KO:' $_.Exception.Message }"

echo.
echo 5. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    ERREUR "clients.map is not a function" RESOLUE !
echo ========================================
echo.
echo 🔧 PROBLEME IDENTIFIE:
echo ❌ Avant: setClients(data) - data n'etait pas un tableau
echo ✅ Apres: setClients(result.data) - extraction du tableau
echo.
echo 📊 FORMAT API CORRIGE:
echo L'API retourne:
echo {
echo   "success": true,
echo   "data": [array_of_clients],
echo   "total": number
echo }
echo.
echo Le code utilisait: setClients(data)
echo Maintenant utilise: setClients(result.data)
echo.
echo ✅ CORRECTIONS APPLIQUEES:
echo ✅ fetchClients() - Extraction correcte du tableau
echo ✅ fetchContracts() - URL API corrigee
echo ✅ Verification Array.isArray() avant .map()
echo ✅ Gestion des erreurs amelioree
echo ✅ Logs de debugging ajoutes
echo ✅ Valeurs par defaut securisees
echo.
echo 🔄 FLUX CORRIGE:
echo 1. API retourne { success: true, data: [...] }
echo 2. Code extrait result.data (le tableau)
echo 3. setClients(result.data) - tableau valide
echo 4. clients.map() fonctionne correctement
echo.
echo 🛡️ PROTECTIONS AJOUTEES:
echo ✅ Array.isArray(clients) avant .map()
echo ✅ Array.isArray(contracts) avant .map()
echo ✅ Gestion des proprietes manquantes (|| 'N/A')
echo ✅ setClients([]) en cas d'erreur
echo ✅ setContracts([]) en cas d'erreur
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. OBSERVEZ: Plus d'erreur "clients.map is not a function"
echo 5. OBSERVEZ: Liste deroulante des clients fonctionne
echo 6. OBSERVEZ: Selection client → Contrats charges
echo.
echo 💡 POINTS TECHNIQUES:
echo ✅ API /api/clients retourne un objet avec propriete 'data'
echo ✅ API /api/clients/:id/contracts pour les contrats
echo ✅ Verification du type avant utilisation de .map()
echo ✅ Logs console pour debugging
echo ✅ Gestion propre des erreurs HTTP
echo.
echo 🔍 DEBUGGING:
echo - Ouvrez la console du navigateur (F12)
echo - Regardez les logs de chargement des clients
echo - Verifiez le format des reponses API
echo - Logs serveur dans "Backend AquaTrack"
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
