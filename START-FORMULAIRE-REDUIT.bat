@echo off
echo ========================================
echo    AQUATRACK - FORMULAIRE LARGEUR REDUITE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 8 secondes...
timeout /t 8 /nobreak > nul

echo.
echo 4. Demarrage de l'application React Native Web...
cd ..
start "React Native Web AquaTrack" cmd /k "echo Application React Native Web demarre... && npm start"

echo.
echo ========================================
echo    LARGEUR FORMULAIRE REDUITE !
echo ========================================
echo.
echo 📏 MODIFICATIONS APPLIQUEES:
echo ✅ Largeur formulaire reduite
echo ✅ Champs plus compacts
echo ✅ Espacements optimises
echo ✅ Design plus concentre
echo.
echo 🎨 CHANGEMENTS VISUELS:
echo.
echo 📋 CONTENEUR FORMULAIRE:
echo ❌ Avant: margin: 20px (partout)
echo ✅ Apres: marginHorizontal: 40px
echo ✅ Nouveau: maxWidth: 500px
echo ✅ Nouveau: alignSelf: 'center'
echo.
echo 📝 CHAMPS DE SAISIE:
echo ❌ Avant: padding: 12px, fontSize: 16px
echo ✅ Apres: padding: 10px, fontSize: 14px
echo ✅ Nouveau: height: 40px (fixe)
echo ✅ Nouveau: borderRadius: 6px (reduit)
echo.
echo 🏷️ LABELS:
echo ❌ Avant: fontSize: 16px, marginBottom: 8px
echo ✅ Apres: fontSize: 14px, marginBottom: 6px
echo.
echo 📋 LISTES DEROULANTES:
echo ❌ Avant: padding: 12px, fontSize: 16px
echo ✅ Apres: padding: 10px, fontSize: 14px
echo ✅ Nouveau: height: 40px (fixe)
echo ✅ Nouveau: borderRadius: 6px (reduit)
echo.
echo 🔘 BOUTON ENREGISTRER:
echo ❌ Avant: padding: 15px, fontSize: 16px
echo ✅ Apres: padding: 12px, fontSize: 14px
echo ✅ Nouveau: height: 45px (fixe)
echo ✅ Nouveau: borderRadius: 6px (reduit)
echo.
echo 📐 ESPACEMENTS:
echo ❌ Avant: marginBottom: 20px (entre champs)
echo ✅ Apres: marginBottom: 15px (reduit)
echo.
echo 🎯 RESULTATS VISUELS:
echo ✅ Formulaire plus compact et centre
echo ✅ Meilleure utilisation de l'espace
echo ✅ Interface plus professionnelle
echo ✅ Champs alignes et uniformes
echo ✅ Design mobile optimise
echo.
echo 📱 AVANTAGES:
echo ✅ Moins d'espace perdu sur les cotes
echo ✅ Formulaire centre sur l'ecran
echo ✅ Largeur maximale controlee (500px)
echo ✅ Champs plus petits mais lisibles
echo ✅ Interface plus dense
echo ✅ Meilleure ergonomie mobile
echo.
echo 🔧 SPECIFICATIONS TECHNIQUES:
echo - maxWidth: 500px (limite la largeur)
echo - alignSelf: 'center' (centre le formulaire)
echo - marginHorizontal: 40px (marges laterales)
echo - height: 40px (champs uniformes)
echo - fontSize: 14px (texte plus petit)
echo - borderRadius: 6px (coins moins arrondis)
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. OBSERVEZ: Formulaire plus etroit et centre
echo 5. OBSERVEZ: Champs plus compacts
echo 6. TESTEZ: Toutes les fonctionnalites
echo.
echo 💡 COMPARAISON:
echo Avant: Formulaire large qui prend tout l'ecran
echo Apres: Formulaire centre avec largeur optimale
echo.
echo Avant: Champs grands avec beaucoup d'espace
echo Apres: Champs compacts mais confortables
echo.
echo Avant: Espacements genereux
echo Apres: Espacements optimises
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
