@echo off
echo ========================================
echo    AQUATRACK - PERIODE EN PREMIER CHAMP
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    PERIODE MISE EN PREMIER !
echo ========================================
echo.
echo 📅 NOUVEAU ORDRE DES CHAMPS:
echo 1. 📅 Periode (YYYY-MM) - PREMIER CHAMP
echo 2. 📋 Section Contrat
echo 3. 💧 Consommation Precedente
echo 4. 💧 Consommation Actuelle
echo 5. 📊 Nombre de jours
echo 6. 📝 Statut
echo.
echo 🎨 DESIGN PERIODE:
echo ✅ Section dediee avec fond bleu clair
echo ✅ Titre "📅 Periode de Facturation"
echo ✅ Champ mis en evidence avec bordure bleue
echo ✅ Police en gras pour la valeur
echo ✅ Couleur distinctive
echo.
echo 🎯 AVANTAGES:
echo ✅ Periode visible immediatement
echo ✅ Champ le plus important en premier
echo ✅ Design attractif et professionnel
echo ✅ Separation claire des sections
echo.
echo 📱 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Observez la periode en premier champ !
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
