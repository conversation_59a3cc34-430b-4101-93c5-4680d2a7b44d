@echo off
echo ========================================
echo    AQUATRACK - REACT NATIVE CONSOMMATION
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Installation des dependances React Native...
echo Installation de @react-native-picker/picker...
npm install @react-native-picker/picker

echo.
echo 3. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 4. Attente de 8 secondes...
timeout /t 8 /nobreak > nul

echo.
echo 5. Demarrage de l'application React Native...
cd ..
start "React Native AquaTrack" cmd /k "echo Application React Native demarre... && npm start"

echo.
echo ========================================
echo    CONVERSION REACT NATIVE TERMINEE !
echo ========================================
echo.
echo 🔄 CONVERSION EFFECTUEE:
echo ❌ Avant: React Web (HTML/CSS)
echo ✅ Apres: React Native (Mobile)
echo.
echo 📱 COMPOSANTS CONVERTIS:
echo ✅ div → View
echo ✅ input → TextInput
echo ✅ select → Picker
echo ✅ button → TouchableOpacity
echo ✅ form → View (sans onSubmit)
echo ✅ CSS → StyleSheet
echo ✅ alert() → Alert.alert()
echo ✅ window.history.back() → navigation
echo.
echo 🎨 STYLES REACT NATIVE:
echo ✅ StyleSheet.create() utilise
echo ✅ Flexbox pour la mise en page
echo ✅ Elevation et shadows pour les cartes
echo ✅ Couleurs et espacements optimises
echo ✅ Design mobile-first
echo.
echo 📋 FONCTIONNALITES MOBILES:
echo ✅ ScrollView pour le defilement
echo ✅ TouchableOpacity pour les boutons
echo ✅ Picker pour les listes deroulantes
echo ✅ TextInput avec keyboardType
echo ✅ ActivityIndicator pour le loading
echo ✅ Alert.alert() pour les messages
echo.
echo 🔧 DEPENDANCES AJOUTEES:
echo ✅ @react-native-picker/picker
echo ✅ react-native components natifs
echo ✅ StyleSheet pour les styles
echo.
echo 📊 STRUCTURE MOBILE:
echo ✅ Header avec bouton retour
echo ✅ ScrollView pour le contenu
echo ✅ FormContainer avec elevation
echo ✅ Champs optimises pour mobile
echo ✅ Boutons tactiles
echo.
echo 🎯 AMELIORATIONS MOBILES:
echo ✅ Interface tactile optimisee
echo ✅ Tailles de police adaptees
echo ✅ Espacements mobiles
echo ✅ Couleurs contrastees
echo ✅ Feedback visuel
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. OBSERVEZ: Interface React Native mobile
echo 5. TESTEZ: Tous les champs et boutons
echo.
echo 💡 DIFFERENCES PRINCIPALES:
echo - Plus de HTML/CSS classique
echo - Composants React Native natifs
echo - Styles avec StyleSheet
echo - Navigation mobile
echo - Interactions tactiles
echo - Performance mobile optimisee
echo.
echo 📱 COMPATIBILITE:
echo ✅ Android (via React Native)
echo ✅ iOS (via React Native)
echo ✅ Web (via React Native Web)
echo ✅ Emulateurs
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
