@echo off
echo ========================================
echo    AQUATRACK - SECTEUR PREMIER CHAMP
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 8 secondes...
timeout /t 8 /nobreak > nul

echo.
echo 4. Test API secteurs...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000/api/secteurs'; Write-Host '✅ API Secteurs OK:' $r.total 'secteurs disponibles' } catch { Write-Host '❌ API Secteurs KO' }"

echo.
echo 5. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    SECTEUR MAINTENANT EN PREMIER !
echo ========================================
echo.
echo 📍 NOUVEL ORDRE DES CHAMPS:
echo 1. 📍 SECTEUR - PREMIER CHAMP (mis en evidence)
echo 2. 📅 Periode (YYYY-MM)
echo 3. 👤 Client
echo 4. 📋 Contrat
echo 5. 💧 Consommation Precedente
echo 6. 💧 Consommation Actuelle
echo 7. 📊 Nombre de jours
echo.
echo 🎨 DESIGN SPECIAL PREMIER CHAMP:
echo ✅ Fond bleu clair (#f0f8ff)
echo ✅ Bordure bleue (#007bff)
echo ✅ Label plus grand (16px vs 14px)
echo ✅ Label en gras et bleu
echo ✅ Icone 📍 pour identifier le secteur
echo ✅ Padding augmente (12px)
echo ✅ Espacement augmente apres le champ
echo.
echo 🔄 LOGIQUE FONCTIONNELLE:
echo ✅ Chargement automatique des secteurs au demarrage
echo ✅ 5 secteurs de votre base PostgreSQL
echo ✅ Selection optionnelle (peut rester vide)
echo ✅ Sauvegarde avec les autres donnees
echo.
echo 📊 SECTEURS DISPONIBLES:
echo 1. Residentiel
echo 2. Commercial
echo 3. Industriel
echo 4. Secteur Centre
echo 5. Fes
echo.
echo 🎯 AVANTAGES DU PREMIER CHAMP:
echo ✅ Secteur visible immediatement
echo ✅ Categorisation geographique prioritaire
echo ✅ Facilite l'organisation territoriale
echo ✅ Design attractif et professionnel
echo ✅ Separation claire des autres champs
echo.
echo 💡 POURQUOI SECTEUR EN PREMIER:
echo - Permet de categoriser la consommation des le debut
echo - Facilite les analyses par zone geographique
echo - Aide a l'organisation du travail des techniciens
echo - Donne un contexte territorial a la saisie
echo - Optimise les rapports par secteur
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. OBSERVEZ: Secteur en premier avec design special
echo 5. CLIQUEZ: Sur le champ secteur
echo 6. SELECTIONNEZ: Un secteur (ex: "Residentiel")
echo 7. CONTINUEZ: Avec les autres champs
echo 8. ENREGISTREZ: Consommation avec secteur
echo.
echo 🎨 DETAILS VISUELS:
echo - Champ secteur: Fond bleu clair + bordure bleue
echo - Label "📍 Secteur": Plus grand, gras, bleu
echo - Autres champs: Design normal
echo - Separation claire entre secteur et periode
echo - Interface hierarchisee et professionnelle
echo.
echo 📱 EXPERIENCE UTILISATEUR:
echo ✅ Secteur immediatement visible
echo ✅ Priorite donnee a la localisation
echo ✅ Workflow logique: Secteur → Periode → Client
echo ✅ Design intuitif et guide
echo ✅ Feedback visuel clair
echo.
echo 🔍 DEBUGGING:
echo - Serveur: Voir fenetre "Backend AquaTrack"
echo - React: Voir fenetre "React AquaTrack"
echo - API Test: http://localhost:4000/api/secteurs
echo - Console: F12 pour voir les logs
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
