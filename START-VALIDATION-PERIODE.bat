@echo off
echo ========================================
echo    AQUATRACK - VALIDATION PERIODE
echo ========================================

echo.
echo 1. Arret des anciens processus...
taskkill /F /IM node.exe >nul 2>&1

echo.
echo 2. Demarrage du serveur backend...
cd backend
start "Backend AquaTrack" cmd /k "echo Serveur backend demarre... && node server-db.js"

echo.
echo 3. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 4. Demarrage de l'application React...
cd ..
start "React AquaTrack" cmd /k "echo Application React demarre... && npm start"

echo.
echo ========================================
echo    VALIDATION PERIODE ACTIVEE !
echo ========================================
echo.
echo 🚫 RESTRICTIONS IMPLEMENTEES:
echo ❌ Mois actuel: INTERDIT
echo ❌ Mois futurs: INTERDITS
echo ✅ Mois precedents: AUTORISES UNIQUEMENT
echo.
echo 📅 LOGIQUE DE VALIDATION:
echo ✅ Par defaut: Mois precedent selectionne
echo ✅ Validation en temps reel
echo ✅ Message d'erreur si periode invalide
echo ✅ Blocage de soumission si periode incorrecte
echo.
echo 🎨 INTERFACE AMELIOREE:
echo ✅ Texte d'aide: "Saisissez uniquement les mois precedents"
echo ✅ Affichage des mois disponibles
echo ✅ Couleurs distinctives (rouge pour avertissement)
echo ✅ Validation immediate lors de la saisie
echo.
echo 📋 EXEMPLES DE TEST:
echo.
echo Date actuelle: %date%
echo.
echo ✅ VALIDE (mois precedents):
echo    - 2024-12 (decembre 2024)
echo    - 2024-11 (novembre 2024)
echo    - 2024-10 (octobre 2024)
echo.
echo ❌ INVALIDE (mois actuel/futurs):
echo    - 2025-01 (janvier 2025 - mois actuel)
echo    - 2025-02 (fevrier 2025 - mois futur)
echo    - 2025-03 (mars 2025 - mois futur)
echo.
echo 🧪 POUR TESTER:
echo 1. Connectez-<NAME_EMAIL> / Tech123
echo 2. Allez dans "Les Clients"
echo 3. Cliquez sur "Consommation" pour un client
echo 4. Essayez de saisir le mois actuel → ERREUR
echo 5. Essayez de saisir un mois futur → ERREUR
echo 6. Saisissez un mois precedent → OK
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
