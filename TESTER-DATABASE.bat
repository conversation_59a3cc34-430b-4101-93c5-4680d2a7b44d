@echo off
title AquaTrack - Test Base de Donnees
color 0B

echo.
echo ========================================
echo    🗄️  TEST BASE DE DONNEES AQUATRACK
echo ========================================
echo.

echo 🔍 Test de la connexion PostgreSQL...
echo.

node test-auth.js

echo.
echo 📋 Resultats du test affiches ci-dessus
echo.

if %errorlevel% equ 0 (
    echo ✅ Base de donnees OK - Vous pouvez demarrer l'application !
    echo.
    echo 🚀 Voulez-vous demarrer AquaTrack maintenant ? (O/N)
    set /p choice="Votre choix: "
    if /i "%choice%"=="O" (
        echo.
        echo 🚀 Demarrage d'AquaTrack...
        call DEMARRER-AQUATRACK.bat
    )
) else (
    echo ❌ Probleme avec la base de donnees !
    echo.
    echo 🔧 Solutions:
    echo    1. <PERSON><PERSON><PERSON> PostgreSQL
    echo    2. <PERSON><PERSON><PERSON> la base 'Facturation'
    echo    3. Verifiez les identifiants
)

echo.
pause
