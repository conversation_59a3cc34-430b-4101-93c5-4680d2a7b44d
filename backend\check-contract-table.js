const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function checkContractTable() {
  try {
    console.log('🔍 Vérification de la table contract...');
    
    // Vérifier si la table existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'contract'
      );
    `);
    
    console.log('📋 Table contract existe:', tableExists.rows[0].exists);
    
    if (tableExists.rows[0].exists) {
      // Compter les enregistrements
      const count = await pool.query('SELECT COUNT(*) as total FROM contract');
      console.log('📊 Nombre de contrats:', count.rows[0].total);
      
      if (parseInt(count.rows[0].total) > 0) {
        // Afficher quelques contrats
        const contracts = await pool.query('SELECT * FROM contract LIMIT 5');
        console.log('\n📋 Premiers contrats:');
        contracts.rows.forEach(contract => {
          console.log(`   - ID: ${contract.idcontract}, Code QR: ${contract.codeqr}, Client ID: ${contract.idclient}`);
        });
      } else {
        console.log('⚠️ La table contract est vide');
        
        // Créer quelques contrats de test
        console.log('\n📝 Création de contrats de test...');
        
        // Récupérer quelques clients
        const clients = await pool.query('SELECT idclient, nom, prenom FROM client LIMIT 3');
        
        if (clients.rows.length > 0) {
          for (let i = 0; i < clients.rows.length; i++) {
            const client = clients.rows[i];
            const codeQr = `QR-${client.nom.toUpperCase()}-${String(i + 1).padStart(3, '0')}`;
            
            await pool.query(`
              INSERT INTO contract (
                codeqr, 
                datecontract, 
                idclient, 
                marquecompteur, 
                numseriecompteur,
                posx,
                posy
              ) VALUES ($1, $2, $3, $4, $5, $6, $7)
            `, [
              codeQr,
              new Date(),
              client.idclient,
              'SAGEMCOM T210-D',
              `SN${String(Math.floor(Math.random() * 100000)).padStart(5, '0')}`,
              `${33.5 + Math.random() * 0.1}`, // Latitude approximative
              `${-7.6 + Math.random() * 0.1}`  // Longitude approximative
            ]);
            
            console.log(`   ✅ Contrat créé pour ${client.nom} ${client.prenom}: ${codeQr}`);
          }
          
          // Vérifier à nouveau
          const newCount = await pool.query('SELECT COUNT(*) as total FROM contract');
          console.log(`\n📊 Nouveaux contrats créés: ${newCount.rows[0].total}`);
        }
      }
    } else {
      console.log('❌ La table contract n\'existe pas');
      console.log('\n📝 Création de la table contract...');
      
      await pool.query(`
        CREATE TABLE contract (
          idcontract SERIAL PRIMARY KEY,
          codeqr VARCHAR(100),
          datecontract TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          idclient INTEGER REFERENCES client(idclient),
          idsecteur INTEGER,
          marquecompteur VARCHAR(100),
          numseriecompteur VARCHAR(100),
          posx VARCHAR(50),
          posy VARCHAR(50),
          datedebut TIMESTAMP,
          datefin TIMESTAMP
        );
      `);
      
      console.log('✅ Table contract créée');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
    console.error('Détails:', error);
  } finally {
    await pool.end();
  }
}

checkContractTable();
