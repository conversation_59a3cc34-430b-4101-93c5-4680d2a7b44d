const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function checkUsers() {
  console.log('🔍 Vérification des utilisateurs dans la base...');

  try {
    // Vérifier la structure de la table utilisateur
    console.log('\n📋 Structure de la table utilisateur:');
    const structure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'utilisateur'
      ORDER BY ordinal_position
    `);
    
    structure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
    });

    // Lister tous les utilisateurs avec leurs mots de passe
    console.log('\n👥 Utilisateurs dans la base:');
    const users = await pool.query('SELECT * FROM utilisateur ORDER BY idtech');
    
    if (users.rows.length === 0) {
      console.log('❌ Aucun utilisateur trouvé dans la table');
    } else {
      users.rows.forEach(user => {
        console.log(`   ID: ${user.idtech}`);
        console.log(`   Nom: ${user.nom} ${user.prenom}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Mot de passe: ${user.motdepass || user.password || 'NON DÉFINI'}`);
        console.log(`   Rôle: ${user.role}`);
        console.log(`   ---`);
      });
    }

    // Test spé<NAME_EMAIL>
    console.log('\n🔍 Test spé<NAME_EMAIL>:');
    const techUser = await pool.query('SELECT * FROM utilisateur WHERE email = $1', ['<EMAIL>']);
    
    if (techUser.rows.length > 0) {
      const user = techUser.rows[0];
      console.log('✅ Utilisateur trouvé:');
      console.log(`   Email: ${user.email}`);
      console.log(`   Mot de passe stocké: "${user.motdepass || user.password}"`);
      console.log(`   Rôle: ${user.role}`);
      
      // Test de comparaison de mot de passe
      const testPassword = 'Tech123';
      const storedPassword = user.motdepass || user.password;
      console.log(`   Test mot de passe "${testPassword}" === "${storedPassword}": ${storedPassword === testPassword}`);
    } else {
      console.log('❌ Utilisateur <EMAIL> non trouvé');
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

checkUsers();
