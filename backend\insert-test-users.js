const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function insertTestUsers() {
  console.log('🔍 Vérification et insertion des utilisateurs de test...');

  try {
    // Vérifier si des utilisateurs existent déjà
    const checkUsers = await pool.query('SELECT COUNT(*) as count FROM utilisateur');
    const userCount = parseInt(checkUsers.rows[0].count);
    
    console.log(`📊 Nombre d'utilisateurs existants: ${userCount}`);

    if (userCount === 0) {
      console.log('📝 Insertion des utilisateurs de test...');
      
      // Insérer les utilisateurs de test
      const insertQuery = `
        INSERT INTO utilisateur (nom, prenom, adresse, tel, email, motdepass, role, is_protected)
        VALUES 
        ('Technicien', 'Test', '123 Rue Test', '0612345678', '<EMAIL>', 'Tech123', 'Tech', false),
        ('Admin', 'Test', '456 Avenue Admin', '0623456789', '<EMAIL>', 'Admin123', 'Admin', false)
        ON CONFLICT (email) DO NOTHING
      `;
      
      await pool.query(insertQuery);
      console.log('✅ Utilisateurs de test insérés');
    } else {
      console.log('ℹ️  Des utilisateurs existent déjà');
    }

    // Afficher tous les utilisateurs
    const allUsers = await pool.query('SELECT idtech, nom, prenom, email, role FROM utilisateur ORDER BY role, nom');
    console.log('\n📋 Utilisateurs dans la base:');
    allUsers.rows.forEach(user => {
      console.log(`   - ${user.nom} ${user.prenom} (${user.email}) - Role: ${user.role}`);
    });

    console.log('\n🔑 Comptes de test disponibles:');
    console.log('   - <EMAIL> / Tech123');
    console.log('   - <EMAIL> / Admin123');

  } catch (error) {
    console.error('❌ Erreur lors de l\'insertion des utilisateurs:', error.message);
    
    if (error.code === '42P01') {
      console.log('💡 La table utilisateur n\'existe pas. Créez-la avec:');
      console.log(`
CREATE TABLE utilisateur (
    idtech SERIAL PRIMARY KEY,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    adresse VARCHAR(255),
    tel VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    motdepass VARCHAR(100),
    role VARCHAR(10) CHECK (role IN ('Admin', 'Tech')),
    is_protected BOOLEAN DEFAULT false
);
      `);
    }
  } finally {
    await pool.end();
  }
}

// Exécuter l'insertion
insertTestUsers();
