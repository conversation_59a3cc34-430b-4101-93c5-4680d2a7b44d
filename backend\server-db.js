const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();
const PORT = 4000; // Port fixe pour le backend

// Configuration de la base de données PostgreSQL
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'Facturation',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8081', 'http://***********:8081'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée à', new Date().toLocaleTimeString());
  res.json({
    message: 'Serveur AquaTrack avec base de données fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route d'authentification
app.post('/api/auth/login', async (req, res) => {
  console.log('📥 Requête POST /api/auth/login reçue');
  console.log('📊 Body reçu:', req.body);

  try {
    const { email, password } = req.body;
    console.log('🔐 Tentative de connexion pour:', email);
    console.log('🔑 Mot de passe reçu:', password ? '***' : 'VIDE');
    
    const userQuery = `
      SELECT idtech, nom, prenom, email, role, password
      FROM utilisateur
      WHERE email = $1
    `;

    const result = await pool.query(userQuery, [email]);

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];

    // Vérification du mot de passe
    if (user.password !== password) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    console.log('✅ Connexion réussie pour:', email, 'Role:', user.role);

    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('❌ Erreur lors de l\'authentification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification'
    });
  }
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les clients...');

    const clientsQuery = `
      SELECT
        idclient,
        nom,
        prenom,
        adresse,
        ville,
        tel,
        email,
        statut
      FROM client
      ORDER BY nom, prenom
    `;

    const result = await pool.query(clientsQuery);
    console.log(`✅ ${result.rows.length} clients trouvés`);

    res.json({
      success: true,
      clients: result.rows,
      total: result.rows.length,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`🔍 Récupération des contrats pour le client ID: ${id}`);

    const contractsQuery = `
      SELECT
        idcontract,
        codeqr,
        datecontract,
        marquecompteur,
        numseriecompteur,
        posx,
        posy
      FROM contract
      WHERE idclient = $1
      ORDER BY datecontract DESC
    `;

    const result = await pool.query(contractsQuery, [id]);
    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer la dernière consommation d'un contrat
app.get('/api/contracts/:id/last-consumption', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`🔍 Récupération de la dernière consommation pour le contrat ID: ${id}`);

    const lastConsumptionQuery = `
      SELECT
        idcons,
        consommationpre,
        consommationactuelle,
        idcont,
        idtech,
        idtranch,
        jours,
        periode,
        status
      FROM consommation
      WHERE idcont = $1
      ORDER BY periode DESC, idcons DESC
      LIMIT 1
    `;

    const result = await pool.query(lastConsumptionQuery, [id]);

    if (result.rows.length > 0) {
      const lastConsumption = result.rows[0];
      console.log(`✅ Dernière consommation trouvée pour le contrat ${id}:`, lastConsumption.consommationactuelle, 'm³');

      res.json({
        success: true,
        lastConsumption: lastConsumption,
        message: 'Dernière consommation récupérée avec succès'
      });
    } else {
      console.log(`ℹ️ Aucune consommation trouvée pour le contrat ${id}`);
      res.json({
        success: true,
        lastConsumption: null,
        message: 'Aucune consommation précédente trouvée'
      });
    }

  } catch (error) {
    console.error('❌ Erreur lors de la récupération de la dernière consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la dernière consommation',
      error: error.message
    });
  }
});

// Route pour récupérer tous les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('🔍 Récupération de tous les secteurs...');

    const secteursQuery = `
      SELECT
        ids,
        nom,
        latitude,
        longitude
      FROM secteur
      ORDER BY nom ASC
    `;

    const result = await pool.query(secteursQuery);
    console.log(`✅ ${result.rows.length} secteur(s) trouvé(s)`);

    res.json({
      success: true,
      total: result.rows.length,
      data: result.rows
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour récupérer les clients d'un secteur spécifique
app.get('/api/secteurs/:id/clients', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`🔍 Récupération des clients du secteur ID: ${id}`);

    const clientsQuery = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.statut,
        c.ids as secteur_id,
        s.nom as secteur_nom,
        s.latitude as secteur_latitude,
        s.longitude as secteur_longitude,
        ct.posx as latitude,
        ct.posy as longitude
      FROM client c
      INNER JOIN secteur s ON c.ids = s.ids
      LEFT JOIN contract ct ON c.idclient = ct.idclient
      WHERE c.ids = $1
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(clientsQuery, [id]);
    console.log(`✅ ${result.rows.length} client(s) trouvé(s) dans le secteur ${id}`);

    // Ajouter des coordonnées par défaut si elles n'existent pas dans les contrats
    const clientsAvecCoordonnees = result.rows.map(client => ({
      ...client,
      latitude: client.latitude || (client.secteur_latitude + (Math.random() - 0.5) * 0.01),
      longitude: client.longitude || (client.secteur_longitude + (Math.random() - 0.5) * 0.01)
    }));

    res.json({
      success: true,
      count: result.rows.length,
      data: clientsAvecCoordonnees,
      secteur: result.rows.length > 0 ? {
        id: result.rows[0].secteur_id,
        nom: result.rows[0].secteur_nom,
        latitude: result.rows[0].secteur_latitude,
        longitude: result.rows[0].secteur_longitude
      } : null
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients du secteur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients du secteur',
      error: error.message
    });
  }
});

// Route pour enregistrer une nouvelle consommation
app.post('/api/consommations', async (req, res) => {
  try {
    console.log('📝 Nouvelle demande d\'enregistrement de consommation...');
    console.log('Données reçues:', req.body);

    const {
      periode,
      consommationPre,
      consommationActuelle,
      jours,
      idcont,
      idsecteur,
      idtech,
      idtranch,
      status
    } = req.body;

    // Validation des données requises
    if (!periode || !consommationActuelle || !idcont) {
      return res.status(400).json({
        success: false,
        message: 'Données manquantes: période, consommation actuelle et contrat sont requis'
      });
    }

    // Vérifier que le contrat existe
    const contractCheck = await pool.query('SELECT idcontract FROM contract WHERE idcontract = $1', [idcont]);
    if (contractCheck.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Contrat non trouvé'
      });
    }

    // Insérer la nouvelle consommation
    const insertQuery = `
      INSERT INTO consommation (
        consommationpre,
        consommationactuelle,
        idcont,
        idsecteur,
        idtech,
        idtranch,
        jours,
        periode,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      parseInt(consommationPre) || 0,
      parseInt(consommationActuelle),
      parseInt(idcont),
      parseInt(idsecteur) || null, // ID secteur (peut être null)
      parseInt(idtech) || 1, // ID technicien par défaut
      parseInt(idtranch) || 1, // ID tranche par défaut
      parseInt(jours) || 30,
      periode,
      status || 'En cours'
    ];

    console.log('🔄 Insertion avec les valeurs:', values);

    const result = await pool.query(insertQuery, values);
    const newConsommation = result.rows[0];

    console.log('✅ Consommation enregistrée avec succès:', newConsommation.idcons);

    res.json({
      success: true,
      message: 'Consommation enregistrée avec succès',
      data: newConsommation
    });

  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement de la consommation:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement de la consommation',
      error: error.message
    });
  }
});

// Route pour lister les utilisateurs
app.get('/api/auth/users', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT idtech, nom, prenom, email, role, is_protected
      FROM utilisateur
      ORDER BY role, nom
    `);
    res.json({
      success: true,
      users: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route de test de la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    const result = await pool.query('SELECT NOW() as current_time, COUNT(*) as client_count FROM client');
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].current_time,
      client_count: result.rows[0].client_count
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur'
  });
});

// Démarrage du serveur
console.log('🚀 Démarrage du serveur AquaTrack avec base de données...');
app.listen(PORT, () => {
  console.log(`✅ Serveur démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('   GET  / - Test serveur');
  console.log('   POST /api/auth/login - Authentification');
  console.log('   GET  /api/clients - Liste des clients');
  console.log('   GET  /api/clients/:id/contracts - Contrats d\'un client');
  console.log('   GET  /api/contracts/:id/last-consumption - Dernière consommation');
  console.log('   GET  /api/secteurs - Liste des secteurs');
  console.log('   GET  /api/secteurs/:id/clients - Clients d\'un secteur');
  console.log('   POST /api/consommations - Enregistrer une consommation');
  console.log('   GET  /api/test-db - Test base de données');
  console.log('🔧 Base de données: PostgreSQL "Facturation"');
  console.log('🎯 Prêt à recevoir des requêtes !');
});

// Gestion des erreurs de processus
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

module.exports = app;
