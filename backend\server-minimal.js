// Serveur minimal avec base de données PostgreSQL
const express = require('express');
const cors = require('cors');
const clientsRoutes = require('./api/clients');
const authRoutes = require('./api/auth');

const app = express();
const PORT = 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8081', 'http://***********:8081'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes API
app.use('/api', authRoutes);
app.use('/api', clientsRoutes);

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée à', new Date().toLocaleTimeString());
  res.json({
    message: 'Serveur AquaTrack minimal fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});



// Démarrage du serveur
console.log('🚀 Démarrage du serveur minimal...');
app.listen(PORT, () => {
  console.log(`✅ Serveur démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('   GET  / - Test serveur');
  console.log('   POST /api/auth/login - Authentification');
  console.log('🔧 Comptes de test:');
  console.log('   <EMAIL> / Tech123');
});

// Gestion des erreurs
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});
