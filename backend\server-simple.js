const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

console.log('🚀 Démarrage du serveur AquaTrack simplifié...');

// Configuration de la base de données PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données au démarrage
pool.connect()
  .then(client => {
    console.log('✅ Connexion PostgreSQL réussie');
    client.release();
  })
  .catch(err => {
    console.error('❌ Erreur connexion PostgreSQL:', err.message);
  });

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.path} - ${new Date().toLocaleTimeString()}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur AquaTrack simplifié fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route d'authentification
app.post('/api/auth/login', async (req, res) => {
  console.log('📥 Requête POST /api/auth/login reçue');
  console.log('📊 Body reçu:', req.body);

  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      console.log('❌ Email ou mot de passe manquant');
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }
    
    console.log('🔐 Tentative de connexion pour:', email);
    
    const userQuery = `
      SELECT idtech, nom, prenom, email, role, password
      FROM utilisateur
      WHERE email = $1
    `;

    console.log('🔍 Exécution de la requête SQL...');
    const result = await pool.query(userQuery, [email]);
    console.log('📊 Résultat requête:', result.rows.length, 'utilisateur(s) trouvé(s)');

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('👤 Utilisateur trouvé:', user.nom, user.prenom);
    console.log('🔑 Mot de passe stocké:', user.password);
    console.log('🔑 Mot de passe reçu:', password);

    // Vérification du mot de passe
    if (user.password !== password) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    console.log('✅ Connexion réussie pour:', email, 'Role:', user.role);

    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('❌ Erreur lors de l\'authentification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification',
      error: error.message
    });
  }
});

// Route pour lister les utilisateurs (debug)
app.get('/api/auth/users', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/auth/users');
    const result = await pool.query('SELECT idtech, nom, prenom, email, role FROM utilisateur');
    console.log('✅ Utilisateurs récupérés:', result.rows.length);
    
    res.json({
      success: true,
      users: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route pour récupérer tous les clients
app.get('/api/clients', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/clients');
    console.log('🔍 Récupération de tous les clients...');

    // Vérifier si la table client existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'client'
      );
    `);
    console.log('📋 Table client existe:', tableExists.rows[0].exists);

    if (!tableExists.rows[0].exists) {
      console.log('❌ Table client n\'existe pas!');
      return res.status(404).json({
        success: false,
        message: 'Table client non trouvée',
        data: [],
        total: 0
      });
    }

    const clientsQuery = `
      SELECT
        idclient,
        nom,
        prenom,
        adresse,
        ville,
        tel,
        email,
        statut
      FROM client
      ORDER BY nom, prenom
    `;

    console.log('📝 Exécution de la requête clients...');
    const result = await pool.query(clientsQuery);
    console.log(`✅ ${result.rows.length} clients trouvés`);

    res.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      count: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients',
      error: error.message
    });
  }
});

// Route pour récupérer tous les secteurs
app.get('/api/secteurs', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/secteurs');
    console.log('🔍 Récupération de tous les secteurs...');

    const secteursQuery = `
      SELECT ids, nom, latitude, longitude
      FROM secteur
      ORDER BY nom
    `;

    const result = await pool.query(secteursQuery);
    console.log(`✅ ${result.rows.length} secteur(s) trouvé(s)`);

    res.json({
      success: true,
      data: result.rows,
      total: result.rows.length
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des secteurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des secteurs',
      error: error.message
    });
  }
});

// Route pour récupérer les contrats d'un client
app.get('/api/clients/:id/contracts', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`📥 Requête GET /api/clients/${id}/contracts`);
    console.log(`🔍 Récupération des contrats pour le client ID: ${id}`);

    const contractsQuery = `
      SELECT
        idcontract,
        codeqr,
        datecontract,
        marquecompteur,
        numseriecompteur,
        posx,
        posy
      FROM contract
      WHERE idclient = $1
      ORDER BY datecontract DESC
    `;

    const result = await pool.query(contractsQuery, [id]);
    console.log(`✅ ${result.rows.length} contrat(s) trouvé(s) pour le client ${id}`);

    res.json({
      success: true,
      count: result.rows.length,
      data: result.rows
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des contrats:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des contrats',
      error: error.message
    });
  }
});

// Route pour récupérer les clients d'un secteur
app.get('/api/secteurs/:id/clients', async (req, res) => {
  const { id } = req.params;

  try {
    console.log(`📥 Requête GET /api/secteurs/${id}/clients`);
    console.log(`🔍 Récupération des clients du secteur ID: ${id}`);

    const clientsQuery = `
      SELECT
        c.idclient,
        c.nom,
        c.prenom,
        c.adresse,
        c.ville,
        c.tel,
        c.email,
        c.statut,
        c.ids as secteur_id,
        s.nom as secteur_nom,
        s.latitude as secteur_latitude,
        s.longitude as secteur_longitude,
        ct.posx as latitude,
        ct.posy as longitude
      FROM client c
      INNER JOIN secteur s ON c.ids = s.ids
      LEFT JOIN contract ct ON c.idclient = ct.idclient
      WHERE c.ids = $1
      ORDER BY c.nom, c.prenom
    `;

    const result = await pool.query(clientsQuery, [id]);
    console.log(`✅ ${result.rows.length} client(s) trouvé(s) dans le secteur ${id}`);

    // Ajouter des coordonnées par défaut si elles n'existent pas dans les contrats
    const clientsAvecCoordonnees = result.rows.map(client => ({
      ...client,
      latitude: client.latitude || (client.secteur_latitude + (Math.random() - 0.5) * 0.01),
      longitude: client.longitude || (client.secteur_longitude + (Math.random() - 0.5) * 0.01)
    }));

    res.json({
      success: true,
      count: result.rows.length,
      data: clientsAvecCoordonnees,
      secteur: result.rows.length > 0 ? {
        id: result.rows[0].secteur_id,
        nom: result.rows[0].secteur_nom,
        latitude: result.rows[0].secteur_latitude,
        longitude: result.rows[0].secteur_longitude
      } : null
    });

  } catch (error) {
    console.error('❌ Erreur lors de la récupération des clients du secteur:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des clients du secteur',
      error: error.message
    });
  }
});

// Route de test de la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('📥 Test de la base de données');
    const result = await pool.query('SELECT NOW() as current_time, COUNT(*) as user_count FROM utilisateur');

    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].current_time,
      user_count: result.rows[0].user_count
    });
  } catch (error) {
    console.error('❌ Erreur test DB:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('   GET  / - Test serveur');
  console.log('   POST /api/auth/login - Authentification');
  console.log('   GET  /api/auth/users - Liste des utilisateurs');
  console.log('   GET  /api/clients - Liste des clients');
  console.log('   GET  /api/secteurs - Liste des secteurs');
  console.log('   GET  /api/clients/:id/contracts - Contrats d\'un client');
  console.log('   GET  /api/secteurs/:id/clients - Clients d\'un secteur');
  console.log('   GET  /api/test-db - Test base de données');
  console.log('🎯 Prêt à recevoir des requêtes !');
});

// Gestion des erreurs de processus
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

module.exports = app;
