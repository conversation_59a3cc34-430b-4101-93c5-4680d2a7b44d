const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

console.log('🚀 Démarrage du serveur AquaTrack simplifié...');

// Configuration de la base de données PostgreSQL
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Test de connexion à la base de données au démarrage
pool.connect()
  .then(client => {
    console.log('✅ Connexion PostgreSQL réussie');
    client.release();
  })
  .catch(err => {
    console.error('❌ Erreur connexion PostgreSQL:', err.message);
  });

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware de logging
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.path} - ${new Date().toLocaleTimeString()}`);
  next();
});

// Route de test
app.get('/', (req, res) => {
  console.log('✅ Route / appelée');
  res.json({
    message: 'Serveur AquaTrack simplifié fonctionnel',
    status: 'OK',
    port: PORT,
    timestamp: new Date().toISOString()
  });
});

// Route d'authentification
app.post('/api/auth/login', async (req, res) => {
  console.log('📥 Requête POST /api/auth/login reçue');
  console.log('📊 Body reçu:', req.body);

  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      console.log('❌ Email ou mot de passe manquant');
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }
    
    console.log('🔐 Tentative de connexion pour:', email);
    
    const userQuery = `
      SELECT idtech, nom, prenom, email, role, password
      FROM utilisateur
      WHERE email = $1
    `;

    console.log('🔍 Exécution de la requête SQL...');
    const result = await pool.query(userQuery, [email]);
    console.log('📊 Résultat requête:', result.rows.length, 'utilisateur(s) trouvé(s)');

    if (result.rows.length === 0) {
      console.log('❌ Utilisateur non trouvé:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    const user = result.rows[0];
    console.log('👤 Utilisateur trouvé:', user.nom, user.prenom);
    console.log('🔑 Mot de passe stocké:', user.password);
    console.log('🔑 Mot de passe reçu:', password);

    // Vérification du mot de passe
    if (user.password !== password) {
      console.log('❌ Mot de passe incorrect pour:', email);
      return res.status(401).json({
        success: false,
        message: 'Email ou mot de passe incorrect'
      });
    }

    console.log('✅ Connexion réussie pour:', email, 'Role:', user.role);

    res.json({
      success: true,
      message: 'Connexion réussie',
      user: {
        idtech: user.idtech,
        nom: user.nom,
        prenom: user.prenom,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('❌ Erreur lors de l\'authentification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur lors de l\'authentification',
      error: error.message
    });
  }
});

// Route pour lister les utilisateurs (debug)
app.get('/api/auth/users', async (req, res) => {
  try {
    console.log('📥 Requête GET /api/auth/users');
    const result = await pool.query('SELECT idtech, nom, prenom, email, role FROM utilisateur');
    console.log('✅ Utilisateurs récupérés:', result.rows.length);
    
    res.json({
      success: true,
      users: result.rows
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des utilisateurs:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur',
      error: error.message
    });
  }
});

// Route de test de la base de données
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('📥 Test de la base de données');
    const result = await pool.query('SELECT NOW() as current_time, COUNT(*) as user_count FROM utilisateur');
    
    res.json({
      success: true,
      message: 'Connexion à la base de données réussie',
      timestamp: result.rows[0].current_time,
      user_count: result.rows[0].user_count
    });
  } catch (error) {
    console.error('❌ Erreur test DB:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur de connexion à la base de données',
      error: error.message
    });
  }
});

// Gestion des erreurs
app.use((err, req, res, next) => {
  console.error('❌ Erreur serveur:', err);
  res.status(500).json({
    success: false,
    message: 'Erreur interne du serveur',
    error: err.message
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`✅ Serveur démarré sur http://localhost:${PORT}`);
  console.log('📡 Routes disponibles:');
  console.log('   GET  / - Test serveur');
  console.log('   POST /api/auth/login - Authentification');
  console.log('   GET  /api/auth/users - Liste des utilisateurs');
  console.log('   GET  /api/test-db - Test base de données');
  console.log('🎯 Prêt à recevoir des requêtes !');
});

// Gestion des erreurs de processus
process.on('uncaughtException', (err) => {
  console.error('❌ Erreur non gérée:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

module.exports = app;
