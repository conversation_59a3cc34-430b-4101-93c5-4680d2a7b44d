const fetch = require('node-fetch');

async function testConsommationAPI() {
  try {
    console.log('🧪 Test de l\'API d\'enregistrement de consommation...');
    
    // Données de test
    const testData = {
      periode: '2025-01',
      consommationPre: 150,
      consommationActuelle: 180,
      jours: 31,
      idcont: 10, // ID d'un contrat existant
      idtech: 1,
      idtranch: 1,
      status: 'En cours'
    };
    
    console.log('📤 Envoi des données:', testData);
    
    const response = await fetch('http://localhost:4000/api/consommations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });
    
    console.log('📡 Statut de la réponse:', response.status);
    console.log('📡 Headers de la réponse:', response.headers.get('content-type'));
    
    const result = await response.text();
    console.log('📥 Réponse brute:', result);
    
    try {
      const jsonResult = JSON.parse(result);
      console.log('📊 Réponse JSON:', jsonResult);
      
      if (jsonResult.success) {
        console.log('✅ Test réussi ! Consommation enregistrée avec l\'ID:', jsonResult.data.idcons);
      } else {
        console.log('❌ Test échoué:', jsonResult.message);
      }
    } catch (parseError) {
      console.log('❌ Erreur de parsing JSON:', parseError.message);
      console.log('📄 Contenu reçu (probablement HTML):', result.substring(0, 200) + '...');
    }
    
  } catch (error) {
    console.error('❌ Erreur de test:', error.message);
  }
}

// Test de vérification du serveur
async function testServerStatus() {
  try {
    console.log('🔍 Vérification du statut du serveur...');
    
    const response = await fetch('http://localhost:4000/');
    const result = await response.text();
    
    console.log('📡 Statut serveur:', response.status);
    console.log('📄 Réponse serveur:', result.substring(0, 100));
    
    if (response.status === 200) {
      console.log('✅ Serveur accessible');
      return true;
    } else {
      console.log('❌ Serveur non accessible');
      return false;
    }
  } catch (error) {
    console.log('❌ Serveur non joignable:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Démarrage des tests API...\n');
  
  // Test 1: Vérifier le serveur
  const serverOK = await testServerStatus();
  console.log('\n' + '='.repeat(50) + '\n');
  
  if (serverOK) {
    // Test 2: Tester l'API de consommation
    await testConsommationAPI();
  } else {
    console.log('⚠️ Impossible de tester l\'API car le serveur n\'est pas accessible');
    console.log('💡 Assurez-vous que le serveur backend est démarré avec: node server-db.js');
  }
}

runTests();
