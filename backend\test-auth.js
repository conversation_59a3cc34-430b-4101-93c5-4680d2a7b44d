const fetch = require('node-fetch');

async function testAuthentication() {
  console.log('🔐 Test de l\'authentification...');
  
  const testCases = [
    {
      email: '<EMAIL>',
      password: 'Tech123',
      description: 'Compte technicien'
    },
    {
      email: '<EMAIL>', 
      password: 'Admin123',
      description: 'Compte admin'
    },
    {
      email: '<EMAIL>',
      password: 'wrongpass',
      description: 'Compte invalide (doit échouer)'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 Test: ${testCase.description}`);
    console.log(`   Email: ${testCase.email}`);
    console.log(`   Password: ${testCase.password}`);
    
    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testCase.email,
          password: testCase.password
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        console.log(`   ✅ Succès: ${data.message}`);
        console.log(`   👤 Utilisateur: ${data.user.nom} ${data.user.prenom}`);
        console.log(`   🎭 Rôle: ${data.user.role}`);
      } else {
        console.log(`   ❌ Échec: ${data.message}`);
      }
      
    } catch (error) {
      console.log(`   💥 Erreur de connexion: ${error.message}`);
    }
  }

  // Test de la route des utilisateurs
  console.log('\n📋 Test de la liste des utilisateurs...');
  try {
    const response = await fetch('http://localhost:4000/api/auth/users');
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ ${data.users.length} utilisateur(s) trouvé(s):`);
      data.users.forEach(user => {
        console.log(`   - ${user.nom} ${user.prenom} (${user.email}) - ${user.role}`);
      });
    } else {
      console.log(`❌ Erreur: ${data.message}`);
    }
  } catch (error) {
    console.log(`💥 Erreur: ${error.message}`);
  }
}

// Attendre un peu que le serveur démarre
setTimeout(testAuthentication, 2000);
