const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testCalculJours() {
  try {
    console.log('🔢 Test du calcul automatique des jours...');
    
    // Récupérer quelques consommations avec leurs périodes
    console.log('\n📊 Consommations existantes avec périodes:');
    const consommations = await pool.query(`
      SELECT 
        cons.idcons,
        cons.periode,
        cons.consommationactuelle,
        cont.codeqr,
        cl.nom,
        cl.prenom
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client cl ON cont.idclient = cl.idclient
      ORDER BY cons.periode DESC
      LIMIT 10
    `);
    
    if (consommations.rows.length > 0) {
      consommations.rows.forEach((cons, index) => {
        console.log(`   ${index + 1}. ${cons.nom} ${cons.prenom} (${cons.codeqr})`);
        console.log(`      Période: ${cons.periode} | Consommation: ${cons.consommationactuelle} m³`);
      });
    }
    
    // Test de calcul de jours entre différentes périodes
    console.log('\n🧮 Tests de calcul de jours:');
    
    const testCases = [
      { from: '2024-10', to: '2024-11', expected: 31 },
      { from: '2024-11', to: '2024-12', expected: 31 },
      { from: '2024-12', to: '2025-01', expected: 31 },
      { from: 'juillet 2025', to: '2025-08', expected: 31 },
      { from: '2024-10', to: '2024-12', expected: 62 },
    ];
    
    testCases.forEach((test, index) => {
      console.log(`\n   Test ${index + 1}: ${test.from} → ${test.to}`);
      
      try {
        // Normaliser les dates
        const normalizeDate = (periode) => {
          if (periode.match(/^\d{4}-\d{2}$/)) {
            return new Date(periode + '-01');
          }
          
          const months = {
            'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04', 
            'mai': '05', 'juin': '06', 'juillet': '07', 'août': '08', 
            'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12'
          };
          
          for (const [monthName, monthNum] of Object.entries(months)) {
            if (periode.toLowerCase().includes(monthName)) {
              const year = periode.match(/\d{4}/)?.[0] || new Date().getFullYear();
              return new Date(`${year}-${monthNum}-01`);
            }
          }
          
          return new Date(periode + '-01');
        };
        
        const fromDate = normalizeDate(test.from);
        const toDate = normalizeDate(test.to);
        
        const diffTime = toDate.getTime() - fromDate.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        console.log(`      De: ${fromDate.toLocaleDateString()}`);
        console.log(`      À:  ${toDate.toLocaleDateString()}`);
        console.log(`      Calculé: ${diffDays} jours`);
        console.log(`      Attendu: ${test.expected} jours`);
        console.log(`      Résultat: ${diffDays === test.expected ? '✅ OK' : '⚠️ Différent'}`);
        
      } catch (error) {
        console.log(`      ❌ Erreur: ${error.message}`);
      }
    });
    
    // Test avec des données réelles de la base
    console.log('\n📋 Test avec données réelles:');
    const realTest = await pool.query(`
      SELECT 
        cons1.periode as periode1,
        cons1.consommationactuelle as cons1,
        cons2.periode as periode2,
        cons2.consommationactuelle as cons2,
        cont.codeqr,
        cl.nom
      FROM consommation cons1
      JOIN consommation cons2 ON cons1.idcont = cons2.idcont AND cons1.idcons < cons2.idcons
      LEFT JOIN contract cont ON cons1.idcont = cont.idcontract
      LEFT JOIN client cl ON cont.idclient = cl.idclient
      ORDER BY cons1.idcons
      LIMIT 3
    `);
    
    if (realTest.rows.length > 0) {
      realTest.rows.forEach((test, index) => {
        console.log(`\n   Exemple ${index + 1}: ${test.nom} (${test.codeqr})`);
        console.log(`      Période 1: ${test.periode1} (${test.cons1} m³)`);
        console.log(`      Période 2: ${test.periode2} (${test.cons2} m³)`);
        
        try {
          const date1 = new Date(test.periode1.includes('-') ? test.periode1 + '-01' : test.periode1);
          const date2 = new Date(test.periode2.includes('-') ? test.periode2 + '-01' : test.periode2);
          
          const diffTime = date2.getTime() - date1.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          
          console.log(`      Jours calculés: ${diffDays}`);
          console.log(`      Consommation différence: ${test.cons2 - test.cons1} m³`);
        } catch (error) {
          console.log(`      ❌ Erreur calcul: ${error.message}`);
        }
      });
    } else {
      console.log('   Aucune donnée comparative trouvée');
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

testCalculJours();
