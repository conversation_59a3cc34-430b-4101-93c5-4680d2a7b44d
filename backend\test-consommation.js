const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testConsommation() {
  try {
    console.log('🔍 Test de la table consommation...');
    
    // Vérifier si la table existe
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'consommation'
      );
    `);
    
    console.log('📋 Table consommation existe:', tableExists.rows[0].exists);
    
    if (!tableExists.rows[0].exists) {
      console.log('📝 Création de la table consommation...');
      await pool.query(`
        CREATE TABLE consommation (
          idcons SERIAL PRIMARY KEY,
          consommationpre INTEGER,
          consommationactuelle INTEGER,
          idcont INTEGER REFERENCES contract(idcontract),
          idtech INTEGER REFERENCES utilisateur(idtech),
          idtranch INTEGER,
          jours INTEGER,
          periode VARCHAR(50),
          status VARCHAR(50)
        );
      `);
      console.log('✅ Table consommation créée');
    }
    
    // Compter les consommations
    const count = await pool.query('SELECT COUNT(*) as total FROM consommation');
    console.log('📊 Nombre de consommations:', count.rows[0].total);
    
    if (parseInt(count.rows[0].total) === 0) {
      console.log('📝 Insertion de données de test...');
      
      // Récupérer quelques contrats
      const contracts = await pool.query('SELECT idcontract FROM contract LIMIT 3');
      
      if (contracts.rows.length > 0) {
        for (let i = 0; i < contracts.rows.length; i++) {
          const contract = contracts.rows[i];
          
          // Insérer plusieurs consommations pour chaque contrat (historique)
          const consumptions = [
            { pre: 0, actuelle: 120, periode: '2024-10', jours: 30 },
            { pre: 120, actuelle: 145, periode: '2024-11', jours: 31 },
            { pre: 145, actuelle: 168, periode: '2024-12', jours: 31 }
          ];
          
          for (const cons of consumptions) {
            await pool.query(`
              INSERT INTO consommation (
                consommationpre, 
                consommationactuelle, 
                idcont, 
                idtech, 
                idtranch, 
                jours, 
                periode, 
                status
              ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            `, [
              cons.pre,
              cons.actuelle,
              contract.idcontract,
              1, // ID technicien par défaut
              1, // ID tranche par défaut
              cons.jours,
              cons.periode,
              'Validée'
            ]);
          }
          
          console.log(`✅ Consommations créées pour le contrat ${contract.idcontract}`);
        }
      }
    }
    
    // Afficher quelques consommations avec leurs contrats
    console.log('\n📋 Consommations avec contrats:');
    const consumptionsWithContracts = await pool.query(`
      SELECT 
        cons.idcons,
        cons.consommationpre,
        cons.consommationactuelle,
        cons.periode,
        cons.status,
        cont.codeqr,
        cl.nom,
        cl.prenom
      FROM consommation cons
      LEFT JOIN contract cont ON cons.idcont = cont.idcontract
      LEFT JOIN client cl ON cont.idclient = cl.idclient
      ORDER BY cons.periode DESC, cons.idcons DESC
      LIMIT 10
    `);
    
    if (consumptionsWithContracts.rows.length > 0) {
      consumptionsWithContracts.rows.forEach(cons => {
        console.log(`   - ${cons.nom} ${cons.prenom} (${cons.codeqr})`);
        console.log(`     Période: ${cons.periode} | ${cons.consommationpre} → ${cons.consommationactuelle} m³`);
        console.log(`     Status: ${cons.status}`);
        console.log(`     ---`);
      });
    }
    
    // Test de l'API pour un contrat spécifique
    console.log('\n🧪 Test de récupération de la dernière consommation...');
    const firstContract = await pool.query('SELECT idcontract FROM contract LIMIT 1');
    
    if (firstContract.rows.length > 0) {
      const contractId = firstContract.rows[0].idcontract;
      console.log(`🔍 Test pour le contrat ID: ${contractId}`);
      
      const lastConsumption = await pool.query(`
        SELECT 
          idcons,
          consommationpre,
          consommationactuelle,
          periode,
          status
        FROM consommation
        WHERE idcont = $1
        ORDER BY periode DESC, idcons DESC
        LIMIT 1
      `, [contractId]);
      
      if (lastConsumption.rows.length > 0) {
        const last = lastConsumption.rows[0];
        console.log(`✅ Dernière consommation trouvée:`);
        console.log(`   Période: ${last.periode}`);
        console.log(`   Consommation: ${last.consommationactuelle} m³`);
        console.log(`   Status: ${last.status}`);
      } else {
        console.log('❌ Aucune consommation trouvée pour ce contrat');
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

testConsommation();
