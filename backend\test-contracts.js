const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testContracts() {
  console.log('🔍 Test des contrats...');

  try {
    // Vérifier la structure de la table contract
    console.log('\n📋 Structure de la table contract:');
    const structure = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'contract'
      ORDER BY ordinal_position
    `);
    
    structure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
    });

    // Compter les contrats
    const countResult = await pool.query('SELECT COUNT(*) as total FROM contract');
    console.log(`\n📊 Nombre total de contrats: ${countResult.rows[0].total}`);

    // Lister quelques contrats avec leurs clients
    console.log('\n📋 Contrats avec clients:');
    const contractsWithClients = await pool.query(`
      SELECT 
        c.idcontract,
        c.codeqr,
        c.datecontract,
        c.idclient,
        c.marquecompteur,
        c.numseriecompteur,
        cl.nom,
        cl.prenom
      FROM contract c
      LEFT JOIN client cl ON c.idclient = cl.idclient
      ORDER BY c.datecontract DESC
      LIMIT 10
    `);

    if (contractsWithClients.rows.length > 0) {
      contractsWithClients.rows.forEach(contract => {
        console.log(`   - Contrat ${contract.idcontract}: ${contract.codeqr}`);
        console.log(`     Client: ${contract.nom} ${contract.prenom} (ID: ${contract.idclient})`);
        console.log(`     Date: ${contract.datecontract}`);
        console.log(`     Compteur: ${contract.marquecompteur} - ${contract.numseriecompteur}`);
        console.log(`     ---`);
      });
    } else {
      console.log('   Aucun contrat trouvé');
    }

    // Test pour un client spécifique
    console.log('\n🔍 Test pour des clients spécifiques:');
    const clientsWithContracts = await pool.query(`
      SELECT 
        cl.idclient,
        cl.nom,
        cl.prenom,
        COUNT(c.idcontract) as nb_contrats
      FROM client cl
      LEFT JOIN contract c ON cl.idclient = c.idclient
      GROUP BY cl.idclient, cl.nom, cl.prenom
      HAVING COUNT(c.idcontract) > 0
      ORDER BY COUNT(c.idcontract) DESC
      LIMIT 5
    `);

    if (clientsWithContracts.rows.length > 0) {
      for (const client of clientsWithContracts.rows) {
        console.log(`\n👤 Client: ${client.nom} ${client.prenom} (ID: ${client.idclient})`);
        console.log(`   Nombre de contrats: ${client.nb_contrats}`);
        
        // Récupérer les contrats de ce client
        const clientContracts = await pool.query(`
          SELECT idcontract, codeqr, datecontract, marquecompteur, numseriecompteur
          FROM contract
          WHERE idclient = $1
          ORDER BY datecontract DESC
        `, [client.idclient]);
        
        clientContracts.rows.forEach((contract, index) => {
          console.log(`   ${index + 1}. ${contract.codeqr} (${new Date(contract.datecontract).toLocaleDateString()})`);
          console.log(`      Compteur: ${contract.marquecompteur} - ${contract.numseriecompteur}`);
        });
      }
    } else {
      console.log('   Aucun client avec contrat trouvé');
    }

  } catch (error) {
    console.error('❌ Erreur:', error.message);
  } finally {
    await pool.end();
  }
}

testContracts();
