const { Pool } = require('pg');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

async function testConnection() {
  console.log('🔍 Test de connexion à PostgreSQL...');
  console.log('📊 Configuration:', {
    user: 'postgres',
    host: 'localhost',
    database: 'Facturation',
    port: 5432
  });

  try {
    // Test de connexion
    console.log('\n1. Test de connexion...');
    const timeResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Connexion réussie:', timeResult.rows[0].current_time);

    // Vérifier si la base de données existe
    console.log('\n2. Vérification de la base de données...');
    const dbResult = await pool.query('SELECT current_database()');
    console.log('✅ Base de données connectée:', dbResult.rows[0].current_database);

    // Lister les tables
    console.log('\n3. Liste des tables...');
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    console.log('📋 Tables trouvées:', tablesResult.rows.map(row => row.table_name));

    // Vérifier la table client
    console.log('\n4. Vérification de la table client...');
    const clientTableResult = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'client'
      ORDER BY ordinal_position
    `);
    
    if (clientTableResult.rows.length > 0) {
      console.log('✅ Table client trouvée avec colonnes:');
      clientTableResult.rows.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type}`);
      });

      // Compter les clients
      const countResult = await pool.query('SELECT COUNT(*) as total FROM client');
      console.log(`📊 Nombre de clients: ${countResult.rows[0].total}`);

      // Afficher quelques clients
      if (parseInt(countResult.rows[0].total) > 0) {
        const clientsResult = await pool.query('SELECT * FROM client LIMIT 5');
        console.log('\n📋 Premiers clients:');
        clientsResult.rows.forEach(client => {
          console.log(`   - ${client.nom} ${client.prenom} (ID: ${client.idclient})`);
        });
      } else {
        console.log('⚠️  Aucun client dans la table');
      }
    } else {
      console.log('❌ Table client non trouvée');
    }

  } catch (error) {
    console.error('❌ Erreur de connexion:', error.message);
    console.error('📋 Détails:', {
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });

    // Suggestions de résolution
    console.log('\n🔧 Suggestions de résolution:');
    if (error.code === 'ECONNREFUSED') {
      console.log('   - PostgreSQL n\'est pas démarré');
      console.log('   - Vérifiez que le service PostgreSQL fonctionne');
    } else if (error.code === '28P01') {
      console.log('   - Mot de passe incorrect');
      console.log('   - Vérifiez le mot de passe dans le fichier');
    } else if (error.code === '3D000') {
      console.log('   - Base de données "Facturation" n\'existe pas');
      console.log('   - Créez la base de données ou vérifiez le nom');
    }
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testConnection();
