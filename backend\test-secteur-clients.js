const fetch = require('node-fetch');

async function testSecteurClients() {
  try {
    console.log('🧪 Test de l\'API secteurs et clients...');
    
    // Test 1: Récupérer tous les secteurs
    console.log('\n1️⃣ Test: Récupération des secteurs');
    const secteursResponse = await fetch('http://localhost:4000/api/secteurs');
    const secteursData = await secteursResponse.json();
    console.log('✅ Secteurs:', secteursData);
    
    if (secteursData.success && secteursData.data.length > 0) {
      const premierSecteur = secteursData.data[0];
      console.log(`\n2️⃣ Test: Récupération des clients du secteur "${premierSecteur.nom}" (ID: ${premierSecteur.ids})`);
      
      // Test 2: Récupérer les clients du premier secteur
      const clientsResponse = await fetch(`http://localhost:4000/api/secteurs/${premierSecteur.ids}/clients`);
      const clientsData = await clientsResponse.json();
      console.log('✅ Clients du secteur:', clientsData);
      
      if (clientsData.success && clientsData.data.length > 0) {
        console.log(`\n📊 Résumé:`);
        console.log(`   - Secteur: ${clientsData.secteur.nom}`);
        console.log(`   - Nombre de clients: ${clientsData.count}`);
        console.log(`   - Clients:`);
        clientsData.data.forEach((client, index) => {
          console.log(`     ${index + 1}. ${client.nom} ${client.prenom} - Lat: ${client.latitude}, Lng: ${client.longitude}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

// Attendre un peu que le serveur démarre, puis lancer le test
setTimeout(testSecteurClients, 2000);
