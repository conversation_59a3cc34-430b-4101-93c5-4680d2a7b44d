const fetch = require('node-fetch');

async function testSecteurComplet() {
  try {
    console.log('🧪 Test complet de la fonctionnalité secteur-clients...');
    
    // Test 1: Récupérer tous les secteurs
    console.log('\n1️⃣ Test: Récupération de tous les secteurs');
    const secteursResponse = await fetch('http://localhost:4000/api/secteurs');
    const secteursData = await secteursResponse.json();
    
    if (secteursData.success) {
      console.log(`✅ ${secteursData.total} secteur(s) trouvé(s):`);
      secteursData.data.forEach((secteur, index) => {
        console.log(`   ${index + 1}. ${secteur.nom} (ID: ${secteur.ids}) - Lat: ${secteur.latitude}, Lng: ${secteur.longitude}`);
      });
      
      // Test 2: Tester chaque secteur
      console.log('\n2️⃣ Test: Récupération des clients pour chaque secteur');
      
      for (const secteur of secteursData.data) {
        console.log(`\n🔍 Secteur: ${secteur.nom} (ID: ${secteur.ids})`);
        
        const clientsResponse = await fetch(`http://localhost:4000/api/secteurs/${secteur.ids}/clients`);
        const clientsData = await clientsResponse.json();
        
        if (clientsData.success) {
          console.log(`   ✅ ${clientsData.count} client(s) trouvé(s):`);
          
          if (clientsData.count > 0) {
            clientsData.data.forEach((client, index) => {
              console.log(`      ${index + 1}. ${client.nom} ${client.prenom}`);
              console.log(`         📍 ${client.adresse}`);
              console.log(`         📞 ${client.tel}`);
              console.log(`         🗺️ Lat: ${client.latitude}, Lng: ${client.longitude}`);
            });
            
            // Simuler l'URL Google Maps
            const marqueurs = clientsData.data.map(client => `${client.latitude},${client.longitude}`);
            const googleMapsUrl = `https://www.google.com/maps?q=${marqueurs[0]}&markers=${marqueurs.join('|')}`;
            console.log(`   🔗 URL Google Maps: ${googleMapsUrl}`);
          } else {
            console.log('      ℹ️ Aucun client dans ce secteur');
          }
        } else {
          console.log(`   ❌ Erreur: ${clientsData.message}`);
        }
      }
      
      // Test 3: Test avec un secteur inexistant
      console.log('\n3️⃣ Test: Secteur inexistant (ID: 999)');
      const testResponse = await fetch('http://localhost:4000/api/secteurs/999/clients');
      const testData = await testResponse.json();
      console.log(`   Résultat: ${testData.success ? 'Succès' : 'Échec'} - ${testData.count || 0} client(s)`);
      
    } else {
      console.log('❌ Erreur lors de la récupération des secteurs');
    }
    
    console.log('\n🎉 Test terminé !');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  }
}

// Lancer le test
testSecteurComplet();
