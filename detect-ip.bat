@echo off
echo ========================================
echo    DETECTION IP LOCALE POUR REACT NATIVE
echo ========================================

echo.
echo Recherche de votre adresse IP locale...
echo.

for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo ✅ IP trouvee: %%b
        echo.
        echo COPIEZ CETTE IP DANS AuthenticationMobile.js:
        echo const API_BASE_URL = 'http://%%b:4000';
        echo.
        goto :found
    )
)

:found
echo.
echo ========================================
echo    INSTRUCTIONS
echo ========================================
echo.
echo 1. Ouvrez le fichier AuthenticationMobile.js
echo 2. Trouvez la ligne: const API_BASE_URL = 'http://***********:4000';
echo 3. Remplacez *********** par: %LOCAL_IP%
echo 4. Sauvegardez le fichier
echo 5. Redemarrez votre application mobile
echo.
echo URL complete: http://%LOCAL_IP%:4000
echo.
pause
