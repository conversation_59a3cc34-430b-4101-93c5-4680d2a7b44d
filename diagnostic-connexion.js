#!/usr/bin/env node

/**
 * Script de diagnostic complet pour résoudre les problèmes de connexion
 */

const { Pool } = require('pg');
const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🔍 DIAGNOSTIC COMPLET - PROBLÈME DE CONNEXION');
console.log('='.repeat(50));
console.log('');

// Fonction pour tester une URL
function testUrl(url, timeout = 5000) {
  return new Promise((resolve) => {
    const req = http.get(url, { timeout }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({ success: true, status: res.statusCode, data });
      });
    });
    
    req.on('error', (err) => {
      resolve({ success: false, error: err.message });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });
  });
}

async function runDiagnostic() {
  console.log('📋 1. VÉRIFICATION DES FICHIERS...');
  
  // Vérifier les fichiers essentiels
  const files = [
    'backend/server-db.js',
    'package.json',
    '.env',
    'src/pages/Login.js'
  ];
  
  for (const file of files) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - Existe`);
    } else {
      console.log(`❌ ${file} - MANQUANT`);
    }
  }
  
  console.log('');
  console.log('📋 2. VÉRIFICATION DE LA CONFIGURATION...');
  
  // Lire le fichier .env
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    console.log('📄 Contenu du fichier .env:');
    envContent.split('\n').forEach(line => {
      if (line.trim() && !line.startsWith('#')) {
        console.log(`   ${line}`);
      }
    });
  }
  
  console.log('');
  console.log('📋 3. TEST DE LA BASE DE DONNÉES...');
  
  try {
    const pool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: 'Facturation',
      password: '123456',
      port: 5432,
    });
    
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL - OK');
    
    // Test de la table utilisateur
    const result = await client.query('SELECT COUNT(*) as count FROM utilisateur');
    console.log(`✅ Table utilisateur - ${result.rows[0].count} utilisateur(s)`);
    
    client.release();
    await pool.end();
  } catch (error) {
    console.log(`❌ Base de données - ERREUR: ${error.message}`);
  }
  
  console.log('');
  console.log('📋 4. TEST DU SERVEUR BACKEND...');
  
  // Tester le serveur backend
  const backendTest = await testUrl('http://localhost:4000');
  if (backendTest.success) {
    console.log(`✅ Backend (port 4000) - OK (Status: ${backendTest.status})`);
  } else {
    console.log(`❌ Backend (port 4000) - ERREUR: ${backendTest.error}`);
  }
  
  // Tester l'API d'authentification
  const authTest = await testUrl('http://localhost:4000/api/auth/login');
  if (authTest.success) {
    console.log(`✅ API Auth - OK (Status: ${authTest.status})`);
  } else {
    console.log(`❌ API Auth - ERREUR: ${authTest.error}`);
  }
  
  console.log('');
  console.log('📋 5. TEST DU SERVEUR FRONTEND...');
  
  // Tester le serveur frontend
  const frontendTest = await testUrl('http://localhost:3000');
  if (frontendTest.success) {
    console.log(`✅ Frontend (port 3000) - OK (Status: ${frontendTest.status})`);
  } else {
    console.log(`❌ Frontend (port 3000) - ERREUR: ${frontendTest.error}`);
  }
  
  console.log('');
  console.log('📋 6. VÉRIFICATION DES PORTS...');
  
  // Vérifier les ports utilisés
  const { exec } = require('child_process');
  
  return new Promise((resolve) => {
    exec('netstat -an | findstr ":3000\\|:4000"', (error, stdout, stderr) => {
      if (stdout) {
        console.log('🔌 Ports en écoute:');
        stdout.split('\n').forEach(line => {
          if (line.trim()) {
            console.log(`   ${line.trim()}`);
          }
        });
      } else {
        console.log('❌ Aucun port 3000 ou 4000 en écoute');
      }
      
      console.log('');
      console.log('📋 7. RECOMMANDATIONS...');
      
      if (!backendTest.success) {
        console.log('🔧 PROBLÈME BACKEND DÉTECTÉ:');
        console.log('   1. Le serveur backend n\'est pas démarré');
        console.log('   2. Démarrez-le avec: node backend/server-db.js');
        console.log('   3. Vérifiez les erreurs dans la console');
      }
      
      if (!frontendTest.success) {
        console.log('🔧 PROBLÈME FRONTEND DÉTECTÉ:');
        console.log('   1. Le serveur frontend n\'est pas démarré');
        console.log('   2. Démarrez-le avec: npm start');
        console.log('   3. Attendez que la compilation soit terminée');
      }
      
      console.log('');
      console.log('🎯 SOLUTION RECOMMANDÉE:');
      console.log('   1. Arrêtez tous les processus');
      console.log('   2. Démarrez le backend: node backend/server-db.js');
      console.log('   3. Attendez 5 secondes');
      console.log('   4. Démarrez le frontend: npm start');
      console.log('   5. Attendez que l\'application se charge');
      
      resolve();
    });
  });
}

// Exécuter le diagnostic
runDiagnostic().catch(console.error);
