@echo off
echo ========================================
echo    CORRECTION AUTOMATIQUE IP
echo ========================================

echo.
echo 1. Detection de l'IP locale...

REM Détecter l'IP locale
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo ✅ IP detectee: %%b
        goto :fix_file
    )
)

:fix_file
echo.
echo 2. Correction du fichier AuthenticationMobile.js...

REM Créer un fichier temporaire avec la bonne IP
powershell -Command "(Get-Content 'AuthenticationMobile.js') -replace 'http://192\.168\.\d+\.\d+:4000', 'http://%LOCAL_IP%:4000' | Set-Content 'AuthenticationMobile.js'"

echo ✅ Fichier corrige avec l'IP: %LOCAL_IP%

echo.
echo 3. Verification...
findstr "API_BASE_URL" AuthenticationMobile.js

echo.
echo ========================================
echo    CORRECTION TERMINEE !
echo ========================================
echo.
echo L'IP a ete automatiquement corrigee dans AuthenticationMobile.js
echo Nouvelle URL: http://%LOCAL_IP%:4000
echo.
echo Vous pouvez maintenant relancer votre application mobile.
echo.
pause
