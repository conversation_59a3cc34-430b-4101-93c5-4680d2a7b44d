import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert, Modal, StyleSheet, ScrollView } from 'react-native';

const ConsommationForm = ({ visible, client, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    periode: '',
    consommationPre: '',
    consommationActuelle: '',
    jours: '',
    idtranch: 1, // Par défaut
    status: 'En cours'
  });
  const [loading, setLoading] = useState(false);
  const [contract, setContract] = useState(null);
  const [contracts, setContracts] = useState([]);
  const [selectedContractId, setSelectedContractId] = useState(null);
  const [lastConsommation, setLastConsommation] = useState(null);
  const [periodeError, setPeriodeError] = useState(null);
  const [consommationError, setConsommationError] = useState(null);

  useEffect(() => {
    if (visible && client) {
      fetchClientContracts();
      fetchLastConsommation();
      // Générer le mois précédent par défaut (pas le mois actuel)
      const now = new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const periode = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`;
      setFormData(prev => ({ ...prev, periode }));
    }
  }, [visible, client]);

  const fetchClientContracts = async () => {
    try {
      console.log('🔍 Récupération des contrats pour le client:', client.idclient);
      const response = await fetch(`http://localhost:4000/api/clients/${client.idclient}/contracts`);
      const data = await response.json();

      console.log('📋 Réponse contrats:', data);

      if (data.success && data.data && data.data.length > 0) {
        setContracts(data.data);

        // Logique de sélection automatique
        if (data.data.length === 1) {
          // Un seul contrat : sélection automatique
          const singleContract = data.data[0];
          setContract(singleContract);
          setSelectedContractId(singleContract.idcontract);
          console.log('✅ Contrat unique sélectionné automatiquement:', singleContract.codeqr);

          // Récupérer la dernière consommation pour ce contrat
          fetchLastConsommation(singleContract.idcontract);
        } else {
          // Plusieurs contrats : sélectionner le plus récent par défaut
          const sortedContracts = data.data.sort((a, b) => new Date(b.datecontract) - new Date(a.datecontract));
          const latestContract = sortedContracts[0];
          setContract(latestContract);
          setSelectedContractId(latestContract.idcontract);
          console.log('✅ Contrat le plus récent sélectionné:', latestContract.codeqr);

          // Récupérer la dernière consommation pour ce contrat
          fetchLastConsommation(latestContract.idcontract);
        }
      } else {
        // Aucun contrat trouvé
        setContracts([]);
        setContract(null);
        setSelectedContractId(null);
        console.log('⚠️ Aucun contrat trouvé pour ce client');
      }
    } catch (error) {
      console.error('❌ Erreur récupération contrats:', error);
      setContracts([]);
      setContract(null);
      setSelectedContractId(null);
    }
  };

  const fetchLastConsommation = async (contractId = null) => {
    try {
      const contractToUse = contractId || (contract && contract.idcontract);

      if (!contractToUse) {
        console.log('⚠️ Aucun contrat disponible pour récupérer la consommation');
        setLastConsommation(null);
        setFormData(prev => ({ ...prev, consommationPre: '0' }));
        return;
      }

      console.log('🔍 Récupération de la dernière consommation pour le contrat:', contractToUse);

      const response = await fetch(`http://localhost:4000/api/contracts/${contractToUse}/last-consumption`);
      const data = await response.json();

      console.log('📊 Réponse dernière consommation:', data);

      if (data.success && data.lastConsumption) {
        const lastCons = data.lastConsumption.consommationactuelle;
        console.log('✅ Dernière consommation trouvée:', lastCons, 'm³');

        setLastConsommation(data.lastConsumption);
        setFormData(prev => ({
          ...prev,
          consommationPre: lastCons.toString()
        }));
      } else {
        console.log('ℹ️ Aucune consommation précédente trouvée pour ce contrat');
        setLastConsommation(null);
        setFormData(prev => ({
          ...prev,
          consommationPre: '0'
        }));
      }
    } catch (error) {
      console.error('❌ Erreur récupération dernière consommation:', error);
      setLastConsommation(null);
      setFormData(prev => ({
        ...prev,
        consommationPre: '0'
      }));
    }
  };

  const calculateJours = () => {
    if (lastConsommation && lastConsommation.periode && formData.periode) {
      try {
        console.log('🔢 Calcul du nombre de jours...');
        console.log('   Dernière période:', lastConsommation.periode);
        console.log('   Période actuelle:', formData.periode);

        // Normaliser les formats de période
        const normalizeDate = (periode) => {
          // Si c'est déjà au format YYYY-MM
          if (periode.match(/^\d{4}-\d{2}$/)) {
            return new Date(periode + '-01');
          }

          // Si c'est un format comme "juillet 2025"
          const months = {
            'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04',
            'mai': '05', 'juin': '06', 'juillet': '07', 'août': '08',
            'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12'
          };

          for (const [monthName, monthNum] of Object.entries(months)) {
            if (periode.toLowerCase().includes(monthName)) {
              const year = periode.match(/\d{4}/)?.[0] || new Date().getFullYear();
              return new Date(`${year}-${monthNum}-01`);
            }
          }

          // Fallback: essayer de parser directement
          return new Date(periode + '-01');
        };

        const currentPeriod = normalizeDate(formData.periode);
        const lastPeriod = normalizeDate(lastConsommation.periode);

        console.log('   Date dernière période:', lastPeriod.toLocaleDateString());
        console.log('   Date période actuelle:', currentPeriod.toLocaleDateString());

        // Calculer la différence (période actuelle - dernière période)
        const diffTime = currentPeriod.getTime() - lastPeriod.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        console.log('   Différence calculée:', diffDays, 'jours');

        // Vérifier que la différence est positive et raisonnable
        if (diffDays > 0 && diffDays <= 365) {
          setFormData(prev => ({ ...prev, jours: diffDays.toString() }));
          console.log('✅ Nombre de jours mis à jour:', diffDays);
        } else if (diffDays <= 0) {
          console.log('⚠️ Période actuelle antérieure à la dernière période');
          setFormData(prev => ({ ...prev, jours: '0' }));
        } else {
          console.log('⚠️ Différence de jours trop importante:', diffDays);
          setFormData(prev => ({ ...prev, jours: '30' })); // Valeur par défaut
        }
      } catch (error) {
        console.error('❌ Erreur calcul jours:', error);
        setFormData(prev => ({ ...prev, jours: '30' })); // Valeur par défaut
      }
    } else {
      console.log('ℹ️ Données insuffisantes pour calculer les jours');
      setFormData(prev => ({ ...prev, jours: '' }));
    }
  };

  useEffect(() => {
    calculateJours();
  }, [formData.periode, lastConsommation]);

  const handleContractChange = (contractId) => {
    const selectedContract = contracts.find(c => c.idcontract === parseInt(contractId));
    if (selectedContract) {
      setContract(selectedContract);
      setSelectedContractId(selectedContract.idcontract);
      console.log('🔄 Contrat sélectionné:', selectedContract.codeqr);

      // Récupérer la dernière consommation pour ce contrat
      fetchLastConsommation(selectedContract.idcontract);
    }
  };

  const validatePeriode = (periode) => {
    if (!periode || periode.length !== 7) {
      return { isValid: false, message: 'Format de période invalide (YYYY-MM)' };
    }

    const [year, month] = periode.split('-');
    const inputDate = new Date(parseInt(year), parseInt(month) - 1, 1);
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    if (inputDate >= currentMonth) {
      return {
        isValid: false,
        message: 'Vous ne pouvez pas saisir le mois actuel ou les mois futurs. Saisissez uniquement les mois précédents.'
      };
    }

    return { isValid: true, message: '' };
  };

  const handlePeriodeChange = (text) => {
    setFormData(prev => ({ ...prev, periode: text }));

    // Validation en temps réel
    if (text.length === 7) {
      const validation = validatePeriode(text);
      if (!validation.isValid) {
        setPeriodeError(validation.message);
      } else {
        setPeriodeError(null);
      }
    } else {
      setPeriodeError(null);
    }
  };

  const getAvailableMonths = () => {
    const now = new Date();
    const months = [];

    // Générer les 6 derniers mois disponibles
    for (let i = 1; i <= 6; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      months.push(monthStr);
    }

    return months;
  };

  const validateConsommationActuelle = (consommationActuelle) => {
    if (!consommationActuelle || consommationActuelle.trim() === '') {
      return { isValid: true, message: '' }; // Pas d'erreur si vide
    }

    const consommationActuelleNum = parseFloat(consommationActuelle);
    const consommationPreNum = lastConsommation ? parseFloat(lastConsommation.consommationactuelle) : 0;

    if (isNaN(consommationActuelleNum)) {
      return {
        isValid: false,
        message: 'Veuillez saisir un nombre valide pour la consommation actuelle.'
      };
    }

    if (consommationActuelleNum <= consommationPreNum) {
      return {
        isValid: false,
        message: `Impossible de saisir cette consommation. La consommation actuelle (${consommationActuelleNum} m³) doit être supérieure à la consommation précédente (${consommationPreNum} m³).`
      };
    }

    return { isValid: true, message: '' };
  };

  const handleConsommationActuelleChange = (text) => {
    setFormData(prev => ({ ...prev, consommationActuelle: text }));

    // Validation en temps réel
    if (text.trim() !== '') {
      const validation = validateConsommationActuelle(text);
      if (!validation.isValid) {
        setConsommationError(validation.message);
      } else {
        setConsommationError(null);
      }
    } else {
      setConsommationError(null);
    }
  };

  const validateForm = () => {
    // Validation de la période
    if (!formData.periode) {
      alert('Veuillez saisir la période');
      return false;
    }

    // Vérifier s'il y a une erreur de période active
    if (periodeError) {
      alert('Veuillez corriger la période avant de continuer');
      return false;
    }

    const periodeValidation = validatePeriode(formData.periode);
    if (!periodeValidation.isValid) {
      alert(periodeValidation.message);
      return false;
    }

    // Validation de la consommation actuelle
    if (!formData.consommationActuelle) {
      alert('Veuillez saisir la consommation actuelle');
      return false;
    }

    // Vérifier s'il y a une erreur de consommation active
    if (consommationError) {
      alert('Veuillez corriger la consommation actuelle avant de continuer');
      return false;
    }

    const consommationValidation = validateConsommationActuelle(formData.consommationActuelle);
    if (!consommationValidation.isValid) {
      alert(consommationValidation.message);
      return false;
    }

    if (!contract) {
      alert('Aucun contrat trouvé pour ce client');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    if (!contract || !contract.idcontract) {
      alert('Erreur: Aucun contrat sélectionné');
      return;
    }

    setLoading(true);
    try {
      const consommationData = {
        periode: formData.periode,
        consommationPre: parseInt(formData.consommationPre || 0),
        consommationActuelle: parseInt(formData.consommationActuelle),
        jours: parseInt(formData.jours || 30),
        idcont: contract.idcontract,
        idtech: 1, // TODO: Récupérer l'ID du technicien connecté
        idtranch: formData.idtranch,
        status: formData.status
      };

      console.log('📤 Envoi des données de consommation:', consommationData);

      const response = await fetch('http://localhost:4000/api/consommations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consommationData),
      });

      const result = await response.json();

      if (result.success) {
        alert('Consommation enregistrée avec succès !');
        onSubmit && onSubmit(result.data);
        onClose();
        resetForm();
      } else {
        alert(`Erreur: ${result.message}`);
      }
    } catch (error) {
      console.error('Erreur soumission:', error);
      alert(`Erreur de connexion: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      periode: '',
      consommationPre: '',
      consommationActuelle: '',
      jours: '',
      idtranch: 1,
      status: 'En cours'
    });
    setContract(null);
    setContracts([]);
    setSelectedContractId(null);
    setLastConsommation(null);
    setPeriodeError(null);
    setConsommationError(null);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <ScrollView style={styles.scrollContainer}>
            <View style={styles.header}>
              <Text style={styles.title}>💧 Saisie Consommation</Text>
              <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>{client?.nom} {client?.prenom}</Text>
              <Text style={styles.clientDetail}>📍 {client?.adresse}</Text>
              <Text style={styles.clientDetail}>🏙️ {client?.ville}</Text>
            </View>

            {/* PÉRIODE - Premier champ en évidence */}
            <View style={styles.periodeSection}>
              <Text style={styles.periodeSectionTitle}>📅 Période de Facturation</Text>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Période (YYYY-MM) *</Text>
                <Text style={styles.periodeHelp}>⚠️ Saisissez uniquement les mois précédents</Text>
                <TextInput
                  style={[
                    styles.input,
                    styles.periodeInput,
                    periodeError && styles.inputError
                  ]}
                  value={formData.periode}
                  onChangeText={handlePeriodeChange}
                  placeholder="2024-12"
                  maxLength={7}
                />

                {/* Bouton d'erreur */}
                {periodeError && (
                  <TouchableOpacity
                    style={styles.errorButton}
                    onPress={() => {
                      Alert.alert(
                        'Période Invalide',
                        periodeError + '\n\nVeuillez saisir une période antérieure au mois actuel.',
                        [
                          {
                            text: 'Compris',
                            onPress: () => {
                              // Remettre le mois précédent par défaut
                              const now = new Date();
                              const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                              const periode = `${lastMonth.getFullYear()}-${String(lastMonth.getMonth() + 1).padStart(2, '0')}`;
                              setFormData(prev => ({ ...prev, periode }));
                              setPeriodeError(null);
                            }
                          }
                        ]
                      );
                    }}
                  >
                    <Text style={styles.errorButtonIcon}>⚠️</Text>
                    <Text style={styles.errorButtonText}>Période Invalide - Cliquez pour corriger</Text>
                  </TouchableOpacity>
                )}

                {!periodeError && (
                  <Text style={styles.availableMonths}>
                    📅 Mois disponibles : {getAvailableMonths().slice(0, 3).join(', ')}...
                  </Text>
                )}
              </View>
            </View>

            {/* Section Contrat */}
            <View style={styles.contractSection}>
              <Text style={styles.sectionTitle}>📋 Contrat</Text>

              {contracts.length === 0 ? (
                // Aucun contrat
                <View style={styles.noContractContainer}>
                  <Text style={styles.noContractIcon}>⚠️</Text>
                  <Text style={styles.noContractText}>
                    Ce client n'admet pas de contrat
                  </Text>
                  <Text style={styles.noContractSubtext}>
                    Veuillez créer un contrat pour ce client avant de saisir une consommation.
                  </Text>
                </View>
              ) : contracts.length === 1 ? (
                // Un seul contrat - Affichage automatique
                <View style={styles.singleContractContainer}>
                  <Text style={styles.contractLabel}>Contrat sélectionné automatiquement :</Text>
                  <View style={styles.contractDisplay}>
                    <Text style={styles.contractCode}>🔗 {contract?.codeqr}</Text>
                    <Text style={styles.contractDetail}>📅 {new Date(contract?.datecontract).toLocaleDateString()}</Text>
                    <Text style={styles.contractDetail}>🔧 {contract?.marquecompteur}</Text>
                    <Text style={styles.contractDetail}>🔢 {contract?.numseriecompteur}</Text>
                  </View>
                </View>
              ) : (
                // Plusieurs contrats - Dropdown de sélection
                <View style={styles.multipleContractsContainer}>
                  <Text style={styles.contractLabel}>Sélectionnez un contrat :</Text>
                  <View style={styles.contractSelector}>
                    {contracts.map((contractItem) => (
                      <TouchableOpacity
                        key={contractItem.idcontract}
                        style={[
                          styles.contractOption,
                          selectedContractId === contractItem.idcontract && styles.selectedContractOption
                        ]}
                        onPress={() => handleContractChange(contractItem.idcontract)}
                      >
                        <View style={styles.contractOptionContent}>
                          <Text style={[
                            styles.contractOptionCode,
                            selectedContractId === contractItem.idcontract && styles.selectedContractText
                          ]}>
                            🔗 {contractItem.codeqr}
                          </Text>
                          <Text style={[
                            styles.contractOptionDetail,
                            selectedContractId === contractItem.idcontract && styles.selectedContractText
                          ]}>
                            📅 {new Date(contractItem.datecontract).toLocaleDateString()}
                          </Text>
                          <Text style={[
                            styles.contractOptionDetail,
                            selectedContractId === contractItem.idcontract && styles.selectedContractText
                          ]}>
                            🔧 {contractItem.marquecompteur}
                          </Text>
                        </View>
                        {selectedContractId === contractItem.idcontract && (
                          <Text style={styles.selectedIcon}>✓</Text>
                        )}
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              )}
            </View>

            <View style={styles.form}>
              <View style={styles.formGroup}>
                <Text style={styles.label}>Consommation Précédente (m³)</Text>
                <TextInput
                  style={[styles.input, styles.readOnlyInput]}
                  value={formData.consommationPre}
                  editable={false}
                  placeholder="Automatique"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Consommation Actuelle (m³) *</Text>
                <TextInput
                  style={[
                    styles.input,
                    consommationError && styles.inputError
                  ]}
                  value={formData.consommationActuelle}
                  onChangeText={handleConsommationActuelleChange}
                  placeholder="Saisir la consommation actuelle"
                  keyboardType="numeric"
                />

                {/* Bouton d'erreur pour consommation invalide */}
                {consommationError && (
                  <TouchableOpacity
                    style={styles.errorButton}
                    onPress={() => {
                      Alert.alert(
                        'Consommation Invalide',
                        consommationError + '\n\nLa consommation actuelle doit toujours être supérieure à la consommation précédente.',
                        [
                          {
                            text: 'Compris',
                            onPress: () => {
                              // Vider le champ pour permettre une nouvelle saisie
                              setFormData(prev => ({ ...prev, consommationActuelle: '' }));
                              setConsommationError(null);
                            }
                          }
                        ]
                      );
                    }}
                  >
                    <Text style={styles.errorButtonIcon}>🚫</Text>
                    <Text style={styles.errorButtonText}>Impossible de saisir cette consommation</Text>
                  </TouchableOpacity>
                )}
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Nombre de jours</Text>
                <TextInput
                  style={[styles.input, styles.readOnlyInput]}
                  value={formData.jours}
                  editable={false}
                  placeholder="Calculé automatiquement"
                />
                {lastConsommation && lastConsommation.periode && formData.periode && formData.jours && (
                  <Text style={styles.joursCalculation}>
                    📊 Calculé entre {lastConsommation.periode} et {formData.periode}
                  </Text>
                )}
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.label}>Statut</Text>
                <TextInput
                  style={styles.input}
                  value={formData.status}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, status: text }))}
                  placeholder="En cours"
                />
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={handleClose}
              >
                <Text style={styles.cancelButtonText}>Annuler</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.submitButton, loading && styles.disabledButton]}
                onPress={handleSubmit}
                disabled={loading}
              >
                <Text style={styles.submitButtonText}>
                  {loading ? 'Enregistrement...' : 'Enregistrer'}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 10,
    width: '70%', // Réduit de 90% à 70%
    maxHeight: '85%', // Légèrement augmenté pour compenser
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  scrollContainer: {
    maxHeight: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15, // Réduit de 20 à 15
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  title: {
    fontSize: 18, // Réduit de 20 à 18
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  clientInfo: {
    padding: 12, // Réduit de 20 à 12
    backgroundColor: '#f8f9fa',
  },
  clientName: {
    fontSize: 16, // Réduit de 18 à 16
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4, // Réduit de 5 à 4
  },
  clientDetail: {
    fontSize: 13, // Réduit de 14 à 13
    color: '#666',
    marginBottom: 1, // Réduit de 2 à 1
  },
  form: {
    padding: 15, // Réduit de 20 à 15
  },
  formGroup: {
    marginBottom: 10, // Réduit de 15 à 10
  },
  label: {
    fontSize: 13, // Réduit de 14 à 13
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4, // Réduit de 5 à 4
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 8, // Réduit de 10 à 8
    fontSize: 14, // Réduit de 16 à 14
    backgroundColor: '#fff',
    height: 40, // Hauteur fixe plus petite
  },
  readOnlyInput: {
    backgroundColor: '#f5f5f5',
    color: '#666',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15, // Réduit de 20 à 15
    paddingTop: 0,
  },
  button: {
    flex: 1,
    padding: 12, // Réduit de 15 à 12
    borderRadius: 5,
    alignItems: 'center',
    marginHorizontal: 5,
    height: 45, // Hauteur fixe plus petite
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#007AFF',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  // Styles pour la section période
  periodeSection: {
    padding: 15,
    backgroundColor: '#e8f4fd',
    borderBottomWidth: 1,
    borderBottomColor: '#bee5eb',
  },
  periodeSectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0c5460',
    marginBottom: 10,
  },
  periodeInput: {
    backgroundColor: '#ffffff',
    borderColor: '#007bff',
    borderWidth: 2,
    fontWeight: 'bold',
    color: '#0c5460',
  },
  periodeHelp: {
    fontSize: 11,
    color: '#dc3545',
    fontStyle: 'italic',
    marginBottom: 5,
    marginTop: 2,
  },
  availableMonths: {
    fontSize: 10,
    color: '#28a745',
    fontStyle: 'italic',
    marginTop: 3,
  },
  inputError: {
    borderColor: '#dc3545',
    borderWidth: 2,
    backgroundColor: '#fff5f5',
  },
  errorButton: {
    backgroundColor: '#dc3545',
    borderRadius: 6,
    padding: 12,
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  errorButtonIcon: {
    fontSize: 18,
    color: '#fff',
    marginRight: 8,
  },
  errorButtonText: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  joursCalculation: {
    fontSize: 11,
    color: '#007bff',
    fontStyle: 'italic',
    marginTop: 3,
  },
  // Styles pour la section contrat
  contractSection: {
    padding: 12, // Réduit de 20 à 12
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  sectionTitle: {
    fontSize: 14, // Réduit de 16 à 14
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10, // Réduit de 15 à 10
  },
  // Aucun contrat
  noContractContainer: {
    alignItems: 'center',
    padding: 15, // Réduit de 20 à 15
    backgroundColor: '#fff3cd',
    borderRadius: 6, // Réduit de 8 à 6
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  noContractIcon: {
    fontSize: 24, // Réduit de 32 à 24
    marginBottom: 8, // Réduit de 10 à 8
  },
  noContractText: {
    fontSize: 14, // Réduit de 16 à 14
    fontWeight: 'bold',
    color: '#856404',
    textAlign: 'center',
    marginBottom: 4, // Réduit de 5 à 4
  },
  noContractSubtext: {
    fontSize: 12, // Réduit de 14 à 12
    color: '#856404',
    textAlign: 'center',
  },
  // Un seul contrat
  singleContractContainer: {
    backgroundColor: '#d4edda',
    borderRadius: 6, // Réduit de 8 à 6
    padding: 12, // Réduit de 15 à 12
    borderWidth: 1,
    borderColor: '#c3e6cb',
  },
  contractLabel: {
    fontSize: 12, // Réduit de 14 à 12
    fontWeight: 'bold',
    color: '#155724',
    marginBottom: 8, // Réduit de 10 à 8
  },
  contractDisplay: {
    backgroundColor: 'white',
    borderRadius: 4, // Réduit de 6 à 4
    padding: 10, // Réduit de 12 à 10
  },
  contractCode: {
    fontSize: 14, // Réduit de 16 à 14
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 3, // Réduit de 4 à 3
  },
  contractDetail: {
    fontSize: 12, // Réduit de 14 à 12
    color: '#666',
    marginBottom: 1, // Réduit de 2 à 1
  },
  // Plusieurs contrats
  multipleContractsContainer: {
    backgroundColor: '#e2e3f3',
    borderRadius: 6, // Réduit de 8 à 6
    padding: 12, // Réduit de 15 à 12
    borderWidth: 1,
    borderColor: '#c5c6d8',
  },
  contractSelector: {
    maxHeight: 150, // Réduit de 200 à 150
  },
  contractOption: {
    backgroundColor: 'white',
    borderRadius: 4, // Réduit de 6 à 4
    padding: 10, // Réduit de 12 à 10
    marginBottom: 6, // Réduit de 8 à 6
    borderWidth: 1,
    borderColor: '#ddd',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedContractOption: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  contractOptionContent: {
    flex: 1,
  },
  contractOptionCode: {
    fontSize: 13, // Réduit de 14 à 13
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 1, // Réduit de 2 à 1
  },
  contractOptionDetail: {
    fontSize: 11, // Réduit de 12 à 11
    color: '#666',
    marginBottom: 0, // Réduit de 1 à 0
  },
  selectedContractText: {
    color: 'white',
  },
  selectedIcon: {
    fontSize: 16, // Réduit de 18 à 16
    color: 'white',
    fontWeight: 'bold',
  },
});

export default ConsommationForm;
