import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator
} from 'react-native';
import { useNavigate } from 'react-router-dom';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async () => {
    if (!formData.email || !formData.password) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    setLoading(true);

    try {
      console.log('🔐 Tentative de connexion pour:', formData.email);
      console.log('🌐 URL API:', 'http://localhost:4000/api/auth/login');

      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password
        }),
      });

      console.log('📡 Réponse serveur status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ Erreur HTTP:', response.status, errorText);
        throw new Error(`Erreur serveur: ${response.status}`);
      }

      const result = await response.json();
      console.log('📊 Réponse serveur:', result);

      if (result.success) {
        console.log('✅ Connexion réussie pour:', result.user.email);
        console.log('👤 Rôle utilisateur:', result.user.role);

        // Stocker les informations utilisateur
        localStorage.setItem('user', JSON.stringify(result.user));

        // Redirection selon le rôle
        if (result.user.role === 'Tech') {
          console.log('🔄 Redirection vers dashboard technicien');
          navigate('/dashboard-technicien');
        } else if (result.user.role === 'Admin') {
          console.log('🔄 Redirection vers dashboard admin');
          navigate('/dashboard-admin');
        } else {
          console.log('🔄 Redirection par défaut vers dashboard technicien');
          navigate('/dashboard-technicien');
        }

        Alert.alert('Succès', `Bienvenue ${result.user.prenom} ${result.user.nom}!`);
      } else {
        console.error('❌ Échec de connexion:', result.message);
        Alert.alert('Erreur de connexion', result.message || 'Email ou mot de passe incorrect');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la connexion:', error);
      
      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        Alert.alert(
          'Erreur de connexion', 
          'Impossible de se connecter au serveur. Vérifiez que le serveur backend est démarré sur le port 4000.'
        );
      } else if (error.message.includes('Erreur serveur')) {
        Alert.alert(
          'Erreur de connexion', 
          'Erreur serveur lors de l\'authentification. Vérifiez la configuration de la base de données.'
        );
      } else {
        Alert.alert(
          'Erreur de connexion', 
          'Une erreur inattendue s\'est produite. Veuillez réessayer.'
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      console.log('🔍 Test de connexion au serveur...');
      const response = await fetch('http://localhost:4000');
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Serveur accessible:', result);
        Alert.alert('Test de connexion', 'Serveur backend accessible ✅');
      } else {
        console.error('❌ Serveur non accessible:', response.status);
        Alert.alert('Test de connexion', `Serveur non accessible (${response.status}) ❌`);
      }
    } catch (error) {
      console.error('❌ Erreur test connexion:', error);
      Alert.alert('Test de connexion', 'Serveur backend non accessible ❌');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.loginCard}>
        {/* Logo et titre */}
        <View style={styles.header}>
          <Text style={styles.logo}>💧</Text>
          <Text style={styles.title}>AquaTrack</Text>
          <Text style={styles.subtitle}>Système de Facturation</Text>
        </View>

        {/* Formulaire de connexion */}
        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>📧 Email</Text>
            <TextInput
              style={styles.input}
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              placeholder="<EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>🔐 Mot de passe</Text>
            <TextInput
              style={styles.input}
              value={formData.password}
              onChangeText={(text) => setFormData(prev => ({ ...prev, password: text }))}
              placeholder="Votre mot de passe"
              secureTextEntry
            />
          </View>

          {/* Bouton de connexion */}
          <TouchableOpacity
            style={[styles.loginButton, loading && styles.disabledButton]}
            onPress={handleLogin}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.loginButtonText}>Se connecter</Text>
            )}
          </TouchableOpacity>

          {/* Bouton de test */}
          <TouchableOpacity
            style={styles.testButton}
            onPress={handleTestConnection}
          >
            <Text style={styles.testButtonText}>🔍 Tester la connexion serveur</Text>
          </TouchableOpacity>
        </View>

        {/* Informations de test */}
        <View style={styles.testInfo}>
          <Text style={styles.testInfoTitle}>🧪 Comptes de test :</Text>
          <Text style={styles.testInfoText}>📧 <EMAIL></Text>
          <Text style={styles.testInfoText}>🔐 Tech123</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loginCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 30,
    width: '100%',
    maxWidth: 400,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    fontSize: 48,
    marginBottom: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007bff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  form: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  loginButton: {
    backgroundColor: '#007bff',
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  testButton: {
    backgroundColor: '#28a745',
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  testInfo: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 15,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  testInfoTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#495057',
    marginBottom: 5,
  },
  testInfoText: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 2,
  },
});

export default Login;
