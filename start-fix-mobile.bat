@echo off
echo ========================================
echo    DEMARRAGE AQUATRACK AVEC CORRECTION IP
echo ========================================

echo.
echo 1. Detection de l'IP locale...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo ✅ IP detectee: %%b
        goto :fix_ip
    )
)

:fix_ip
echo.
echo 2. Correction automatique de l'IP dans AuthenticationMobile.js...
powershell -Command "(Get-Content 'AuthenticationMobile.js') -replace 'http://192\.168\.\d+\.\d+:4000', 'http://%LOCAL_IP%:4000' | Set-Content 'AuthenticationMobile.js'"
echo ✅ IP corrigee: %LOCAL_IP%

echo.
echo 3. Demarrage du serveur backend...
start "Backend AquaTrack" cmd /k "node simple-server.js"

echo.
echo 4. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 5. Test de connexion...
node -e "fetch('http://localhost:4000').then(r => r.json()).then(d => console.log('✅ Serveur OK:', d.message)).catch(e => console.log('❌ Erreur serveur:', e.message))"

echo.
echo 6. Demarrage de l'application mobile...
cd mobile
start "Expo Mobile" cmd /k "npx expo start"

echo.
echo ========================================
echo    AQUATRACK DEMARRE AVEC IP CORRIGEE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 📡 IP Mobile: http://%LOCAL_IP%:4000
echo 📱 Mobile: Expo Dev Tools
echo.
echo INSTRUCTIONS:
echo 1. Attendez que Expo Dev Tools s'ouvre
echo 2. Appuyez sur 'w' pour le mode web
echo 3. Ou scannez le QR code avec Expo Go
echo.
echo Comptes de test:
echo - Tech: <EMAIL> / Tech123
echo - Admin: <EMAIL> / Admin123
echo.
echo Si l'erreur persiste:
echo 1. Verifiez que votre PC et telephone sont sur le meme WiFi
echo 2. Desactivez temporairement le firewall Windows
echo 3. Relancez ce script
echo.
pause
