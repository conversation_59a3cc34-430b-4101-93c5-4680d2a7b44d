@echo off
echo ========================================
echo    DEMARRAGE SERVEUR AQUATRACK - DEBUG
echo ========================================

echo.
echo 1. Verification de Node.js...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js n'est pas installe ou non trouve
    echo Installez Node.js depuis: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js detecte

echo.
echo 2. Arret des processus sur le port 4000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :4000') do (
    echo Arret du processus %%a
    taskkill /PID %%a /F >nul 2>&1
)

echo.
echo 3. Verification du dossier backend...
cd backend
if %errorlevel% neq 0 (
    echo ❌ Dossier backend non trouve
    pause
    exit /b 1
)

echo ✅ <PERSON>ssier backend trouve

echo.
echo 4. Installation des dependances...
npm install

echo.
echo 5. Test de creation d'un serveur simple...
node -e "const express = require('express'); const app = express(); app.get('/', (req, res) => res.json({message: 'Test OK'})); app.listen(4000, () => console.log('Serveur test OK sur port 4000'));" &

echo.
echo 6. Attente de 3 secondes...
timeout /t 3 /nobreak > nul

echo.
echo 7. Test de connexion...
powershell -Command "try { $r = Invoke-RestMethod -Uri 'http://localhost:4000'; Write-Host '✅ Serveur fonctionne:' $r.message } catch { Write-Host '❌ Erreur:' $_.Exception.Message }"

echo.
echo 8. Demarrage du serveur principal...
echo Appuyez sur Ctrl+C pour arreter le serveur
node server-test.js

pause
