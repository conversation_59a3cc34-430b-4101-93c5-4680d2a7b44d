@echo off
echo ========================================
echo    DEMARRAGE SIMPLE AQUATRACK
echo ========================================

echo.
echo 1. Demarrage du serveur backend...
echo.
start "Backend AquaTrack" cmd /k "node simple-server.js"

echo Serveur backend demarre...
echo.

echo 2. Attente de 5 secondes...
timeout /t 5 /nobreak > nul

echo.
echo 3. Test de connexion au serveur...
node -e "fetch('http://localhost:4000').then(r => r.json()).then(d => console.log('✅ Serveur OK:', d.message)).catch(e => console.log('❌ Erreur serveur:', e.message))"

echo.
echo 4. Ouverture du navigateur...
start http://localhost:4000

echo.
echo ========================================
echo    AQUATRACK DEMARRE !
echo ========================================
echo.
echo 📡 Backend: http://localhost:4000
echo 🌐 Test API dans le navigateur
echo.
echo Pour tester l'authentification:
echo POST http://localhost:4000/api/auth/login
echo Body: {"email":"<EMAIL>","password":"Tech123"}
echo.
echo Comptes de test:
echo - Tech: <EMAIL> / Tech123
echo - Admin: <EMAIL> / Admin123
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
