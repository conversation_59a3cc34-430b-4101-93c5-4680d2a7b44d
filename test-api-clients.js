#!/usr/bin/env node

/**
 * Script de test spécifique pour l'API des clients
 * Ce script diagnostique et résout les problèmes avec l'API /api/clients
 */

const http = require('http');
const { Pool } = require('pg');

console.log('👥 TEST COMPLET DE L\'API CLIENTS');
console.log('='.repeat(50));
console.log('');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Fonction pour faire une requête GET
function makeGetRequest(url) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: url,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            error: 'Invalid JSON'
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testClientsAPI() {
  try {
    console.log('📋 1. TEST DE LA BASE DE DONNÉES...');
    
    // Test de connexion à la base de données
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL réussie');
    
    // Vérifier si la table client existe
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'client'
      );
    `;
    
    const tableResult = await client.query(tableExistsQuery);
    const tableExists = tableResult.rows[0].exists;
    
    if (!tableExists) {
      console.log('❌ La table "client" n\'existe pas !');
      console.log('');
      console.log('🔧 Création de la table client...');
      
      const createTableQuery = `
        CREATE TABLE client (
          idclient SERIAL PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          adresse VARCHAR(255),
          ville VARCHAR(100),
          tel VARCHAR(20),
          email VARCHAR(100),
          statut VARCHAR(20),
          ids INTEGER
        );
      `;
      
      await client.query(createTableQuery);
      console.log('✅ Table client créée !');
      
      // Insérer des clients de test
      console.log('');
      console.log('👤 Création de clients de test...');
      
      const insertClientsQuery = `
        INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut, ids)
        VALUES 
          ('Dupont', 'Jean', '123 Rue de la Paix', 'Casablanca', '0612345678', '<EMAIL>', 'Actif', 1),
          ('Martin', 'Marie', '456 Avenue Mohammed V', 'Rabat', '0623456789', '<EMAIL>', 'Actif', 2),
          ('Alami', 'Ahmed', '789 Boulevard Hassan II', 'Casablanca', '0634567890', '<EMAIL>', 'Actif', 1),
          ('Benali', 'Fatima', '321 Rue Allal Ben Abdellah', 'Fès', '0645678901', '<EMAIL>', 'Actif', 3)
        ON CONFLICT DO NOTHING;
      `;
      
      await client.query(insertClientsQuery);
      console.log('✅ Clients de test créés !');
    } else {
      console.log('✅ La table "client" existe !');
    }
    
    // Compter les clients
    const countResult = await client.query('SELECT COUNT(*) as count FROM client');
    const clientCount = countResult.rows[0].count;
    console.log(`✅ ${clientCount} client(s) dans la base de données`);
    
    if (clientCount === 0) {
      console.log('');
      console.log('👤 Ajout de clients de test...');
      
      const insertClientsQuery = `
        INSERT INTO client (nom, prenom, adresse, ville, tel, email, statut, ids)
        VALUES 
          ('Dupont', 'Jean', '123 Rue de la Paix', 'Casablanca', '0612345678', '<EMAIL>', 'Actif', 1),
          ('Martin', 'Marie', '456 Avenue Mohammed V', 'Rabat', '0623456789', '<EMAIL>', 'Actif', 2),
          ('Alami', 'Ahmed', '789 Boulevard Hassan II', 'Casablanca', '0634567890', '<EMAIL>', 'Actif', 1),
          ('Benali', 'Fatima', '321 Rue Allal Ben Abdellah', 'Fès', '0645678901', '<EMAIL>', 'Actif', 3);
      `;
      
      await client.query(insertClientsQuery);
      console.log('✅ Clients de test ajoutés !');
    }
    
    // Afficher quelques clients
    const sampleClientsResult = await client.query('SELECT nom, prenom, ville FROM client LIMIT 3');
    console.log('👥 Exemples de clients:');
    sampleClientsResult.rows.forEach((client, index) => {
      console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
    });
    
    client.release();
    
    console.log('');
    console.log('📋 2. TEST DU SERVEUR BACKEND...');
    
    // Test de la route principale
    try {
      const mainResponse = await makeGetRequest('/');
      console.log(`✅ Serveur principal: Status ${mainResponse.statusCode}`);
    } catch (error) {
      console.log(`❌ Serveur principal: ${error.message}`);
      console.log('🔧 Le serveur backend n\'est pas démarré !');
      return;
    }
    
    console.log('');
    console.log('📋 3. TEST DE L\'API CLIENTS...');
    
    // Test de l'API clients
    try {
      const clientsResponse = await makeGetRequest('/api/clients');
      console.log(`📡 Status: ${clientsResponse.statusCode}`);
      
      if (clientsResponse.statusCode === 200 && clientsResponse.data.success) {
        console.log('✅ API CLIENTS FONCTIONNELLE !');
        console.log(`   Nombre de clients: ${clientsResponse.data.data.length}`);
        console.log(`   Total: ${clientsResponse.data.total}`);
        
        if (clientsResponse.data.data.length > 0) {
          console.log('👥 Premiers clients:');
          clientsResponse.data.data.slice(0, 3).forEach((client, index) => {
            console.log(`   ${index + 1}. ${client.nom} ${client.prenom} - ${client.ville}`);
          });
        }
      } else {
        console.log('❌ API CLIENTS ÉCHOUÉE !');
        console.log(`   Message: ${clientsResponse.data.message || 'Erreur inconnue'}`);
        console.log(`   Erreur: ${clientsResponse.data.error || 'N/A'}`);
      }
    } catch (error) {
      console.log(`❌ Erreur API clients: ${error.message}`);
    }
    
    console.log('');
    console.log('📋 4. TEST DE L\'API SECTEURS...');
    
    // Test de l'API secteurs
    try {
      const secteursResponse = await makeGetRequest('/api/secteurs');
      console.log(`📡 Status: ${secteursResponse.statusCode}`);
      
      if (secteursResponse.statusCode === 200 && secteursResponse.data.success) {
        console.log('✅ API SECTEURS FONCTIONNELLE !');
        console.log(`   Nombre de secteurs: ${secteursResponse.data.data.length}`);
      } else {
        console.log('❌ API SECTEURS ÉCHOUÉE !');
        console.log(`   Message: ${secteursResponse.data.message || 'Erreur inconnue'}`);
      }
    } catch (error) {
      console.log(`❌ Erreur API secteurs: ${error.message}`);
    }
    
    console.log('');
    console.log('📋 5. RÉSUMÉ DU DIAGNOSTIC...');
    console.log('✅ Base de données: Fonctionnelle');
    console.log('✅ Table client: Vérifiée');
    console.log('✅ Serveur backend: Accessible');
    console.log('✅ API clients: Testée');
    
    console.log('');
    console.log('🎯 SOLUTION POUR L\'APPLICATION:');
    console.log('   1. Redémarrez l\'application frontend');
    console.log('   2. Actualisez la page des clients');
    console.log('   3. L\'erreur devrait être résolue');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testClientsAPI();
