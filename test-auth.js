#!/usr/bin/env node

/**
 * Script de test de l'authentification
 * Ce script vérifie la table utilisateur et teste l'authentification
 */

const { Pool } = require('pg');
require('dotenv').config();

console.log('🔐 Test du système d\'authentification...');
console.log('');

// Configuration de la base de données
const dbConfig = {
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: 'Facturation', // Nom fixe correct
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
};

console.log('📋 Configuration de la base de données:');
console.log(`   🏠 Hôte: ${dbConfig.host}:${dbConfig.port}`);
console.log(`   👤 Utilisateur: ${dbConfig.user}`);
console.log(`   🗄️  Base de données: ${dbConfig.database}`);
console.log('');

const pool = new Pool(dbConfig);

async function testAuth() {
  try {
    console.log('🔌 Test de connexion à la base de données...');
    
    const client = await pool.connect();
    console.log('✅ Connexion à PostgreSQL réussie !');
    
    // Vérifier si la table utilisateur existe
    console.log('');
    console.log('🔍 Vérification de la table utilisateur...');
    
    const tableExistsQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'utilisateur'
      );
    `;
    
    const tableResult = await client.query(tableExistsQuery);
    const tableExists = tableResult.rows[0].exists;
    
    if (!tableExists) {
      console.log('❌ La table "utilisateur" n\'existe pas !');
      console.log('');
      console.log('🔧 Création de la table utilisateur...');
      
      const createTableQuery = `
        CREATE TABLE utilisateur (
          idtech SERIAL PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          adresse VARCHAR(255),
          tel VARCHAR(20),
          email VARCHAR(100) UNIQUE,
          password VARCHAR(100),
          role VARCHAR(10) CHECK (role IN ('Admin', 'Tech')),
          is_protected BOOLEAN DEFAULT false
        );
      `;
      
      await client.query(createTableQuery);
      console.log('✅ Table utilisateur créée !');
      
      // Insérer un utilisateur de test
      console.log('');
      console.log('👤 Création d\'un utilisateur de test...');
      
      const insertUserQuery = `
        INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected)
        VALUES ('Technicien', 'Test', '<EMAIL>', 'Tech123', 'Tech', false)
        ON CONFLICT (email) DO NOTHING;
      `;
      
      await client.query(insertUserQuery);
      console.log('✅ Utilisateur de test créé !');
    } else {
      console.log('✅ La table "utilisateur" existe !');
    }
    
    // Vérifier les utilisateurs existants
    console.log('');
    console.log('👥 Utilisateurs dans la base de données:');
    
    const usersQuery = `
      SELECT idtech, nom, prenom, email, role
      FROM utilisateur
      ORDER BY role, nom;
    `;
    
    const usersResult = await client.query(usersQuery);
    
    if (usersResult.rows.length === 0) {
      console.log('⚠️  Aucun utilisateur trouvé !');
      console.log('');
      console.log('👤 Création d\'un utilisateur de test...');
      
      const insertUserQuery = `
        INSERT INTO utilisateur (nom, prenom, email, password, role, is_protected)
        VALUES ('Technicien', 'Test', '<EMAIL>', 'Tech123', 'Tech', false);
      `;
      
      await client.query(insertUserQuery);
      console.log('✅ Utilisateur de test créé !');
      
      // Récupérer à nouveau les utilisateurs
      const newUsersResult = await client.query(usersQuery);
      newUsersResult.rows.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.nom} ${user.prenom} (${user.email}) - Rôle: ${user.role}`);
      });
    } else {
      usersResult.rows.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.nom} ${user.prenom} (${user.email}) - Rôle: ${user.role}`);
      });
    }
    
    // Test d'authentification
    console.log('');
    console.log('🔐 Test d\'<NAME_EMAIL>...');
    
    const authQuery = `
      SELECT idtech, nom, prenom, email, role, password
      FROM utilisateur
      WHERE email = $1;
    `;
    
    const authResult = await client.query(authQuery, ['<EMAIL>']);
    
    if (authResult.rows.length === 0) {
      console.log('❌ Utilisateur <EMAIL> non trouvé !');
    } else {
      const user = authResult.rows[0];
      console.log('✅ Utilisateur trouvé !');
      console.log(`   👤 Nom: ${user.nom} ${user.prenom}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   🎭 Rôle: ${user.role}`);
      console.log(`   🔐 Mot de passe stocké: ${user.password}`);
      
      if (user.password === 'Tech123') {
        console.log('✅ Mot de passe correct !');
      } else {
        console.log('❌ Mot de passe incorrect !');
        console.log('🔧 Correction du mot de passe...');
        
        const updatePasswordQuery = `
          UPDATE utilisateur 
          SET password = 'Tech123' 
          WHERE email = '<EMAIL>';
        `;
        
        await client.query(updatePasswordQuery);
        console.log('✅ Mot de passe corrigé !');
      }
    }
    
    client.release();
    console.log('');
    console.log('🎉 Test d\'authentification terminé avec succès !');
    console.log('');
    console.log('📝 Informations de connexion:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔐 Mot de passe: Tech123');
    
  } catch (error) {
    console.error('');
    console.error('❌ Erreur lors du test d\'authentification:');
    console.error(`   Message: ${error.message}`);
    console.error(`   Code: ${error.code || 'N/A'}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('');
      console.error('🔧 Solutions possibles:');
      console.error('   1. Vérifiez que PostgreSQL est démarré');
      console.error('   2. Vérifiez le port (par défaut 5432)');
    } else if (error.code === '3D000') {
      console.error('');
      console.error('🔧 Solutions possibles:');
      console.error('   1. Créez la base de données "Facturation"');
      console.error('   2. Vérifiez le nom de la base de données');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testAuth();
