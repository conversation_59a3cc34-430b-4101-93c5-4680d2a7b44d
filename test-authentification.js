#!/usr/bin/env node

/**
 * Script de test spécifique pour l'authentification
 * Ce script teste l'API d'authentification et résout les problèmes
 */

const http = require('http');
const { Pool } = require('pg');

console.log('🔐 TEST COMPLET DE L\'AUTHENTIFICATION');
console.log('='.repeat(50));
console.log('');

// Configuration de la base de données
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Facturation',
  password: '123456',
  port: 5432,
});

// Fonction pour faire une requête POST
function makePostRequest(url, data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            error: 'Invalid JSON'
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// Fonction pour faire une requête GET
function makeGetRequest(url) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 4000,
      path: url,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData,
            error: 'Invalid JSON'
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testAuthentication() {
  try {
    console.log('📋 1. TEST DE LA BASE DE DONNÉES...');
    
    // Test de connexion à la base de données
    const client = await pool.connect();
    console.log('✅ Connexion PostgreSQL réussie');
    
    // Vérifier les utilisateurs
    const usersResult = await client.query('SELECT email, role FROM utilisateur');
    console.log(`✅ ${usersResult.rows.length} utilisateur(s) trouvé(s):`);
    usersResult.rows.forEach(user => {
      console.log(`   - ${user.email} (${user.role})`);
    });
    
    client.release();
    
    console.log('');
    console.log('📋 2. TEST DU SERVEUR BACKEND...');
    
    // Test de la route principale
    try {
      const mainResponse = await makeGetRequest('/');
      console.log(`✅ Serveur principal: Status ${mainResponse.statusCode}`);
    } catch (error) {
      console.log(`❌ Serveur principal: ${error.message}`);
      console.log('🔧 Le serveur backend n\'est pas démarré !');
      return;
    }
    
    console.log('');
    console.log('📋 3. TEST DE L\'API D\'AUTHENTIFICATION...');
    
    // Test de la route des utilisateurs
    try {
      const usersResponse = await makeGetRequest('/api/auth/users');
      console.log(`✅ API Users: Status ${usersResponse.statusCode}`);
      if (usersResponse.data && usersResponse.data.users) {
        console.log(`   ${usersResponse.data.users.length} utilisateur(s) via API`);
      }
    } catch (error) {
      console.log(`❌ API Users: ${error.message}`);
    }
    
    // Test de connexion avec les bonnes informations
    console.log('');
    console.log('🔐 Test de <NAME_EMAIL>...');
    
    try {
      const loginResponse = await makePostRequest('/api/auth/login', {
        email: '<EMAIL>',
        password: 'Tech123'
      });
      
      console.log(`📡 Status: ${loginResponse.statusCode}`);
      
      if (loginResponse.statusCode === 200 && loginResponse.data.success) {
        console.log('✅ AUTHENTIFICATION RÉUSSIE !');
        console.log(`   Utilisateur: ${loginResponse.data.user.nom} ${loginResponse.data.user.prenom}`);
        console.log(`   Rôle: ${loginResponse.data.user.role}`);
      } else {
        console.log('❌ AUTHENTIFICATION ÉCHOUÉE !');
        console.log(`   Message: ${loginResponse.data.message || 'Erreur inconnue'}`);
      }
    } catch (error) {
      console.log(`❌ Erreur de connexion: ${error.message}`);
    }
    
    // Test avec de mauvaises informations
    console.log('');
    console.log('🔐 Test avec de mauvaises informations...');
    
    try {
      const badLoginResponse = await makePostRequest('/api/auth/login', {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      
      if (badLoginResponse.statusCode === 401) {
        console.log('✅ Rejet correct des mauvaises informations');
      } else {
        console.log('⚠️  Comportement inattendu pour les mauvaises informations');
      }
    } catch (error) {
      console.log(`❌ Erreur lors du test négatif: ${error.message}`);
    }
    
    console.log('');
    console.log('📋 4. RÉSUMÉ DU DIAGNOSTIC...');
    console.log('✅ Base de données: Fonctionnelle');
    console.log('✅ Serveur backend: Accessible');
    console.log('✅ API d\'authentification: Testée');
    
    console.log('');
    console.log('🎯 SOLUTION POUR RÉSOUDRE L\'ERREUR:');
    console.log('   1. Assurez-vous que le backend est démarré (port 4000)');
    console.log('   2. Assurez-vous que le frontend est démarré (port 3000)');
    console.log('   3. Utilisez: <EMAIL> / Tech123');
    console.log('   4. Attendez que React se charge complètement');
    
  } catch (error) {
    console.error('❌ Erreur lors du test:', error.message);
  } finally {
    await pool.end();
  }
}

// Exécuter le test
testAuthentication();
