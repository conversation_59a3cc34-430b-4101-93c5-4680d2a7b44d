@echo off
echo ========================================
echo    TEST DE CONNEXION AQUATRACK
echo ========================================

echo.
echo 1. Demarrage du serveur backend...
start "Backend Test" cmd /k "node simple-server.js"

echo.
echo 2. Attente de 3 secondes...
timeout /t 3 /nobreak > nul

echo.
echo 3. Test de connexion localhost...
node -e "fetch('http://localhost:4000').then(r => r.json()).then(d => console.log('✅ Localhost OK:', d.message)).catch(e => console.log('❌ Localhost Erreur:', e.message))"

echo.
echo 4. Detection IP locale...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        echo IP detectee: %%b
        goto :test_ip
    )
)

:test_ip
echo.
echo 5. Test de connexion IP locale...
node -e "fetch('http://%LOCAL_IP%:4000').then(r => r.json()).then(d => console.log('✅ IP Locale OK:', d.message)).catch(e => console.log('❌ IP Locale Erreur:', e.message))"

echo.
echo ========================================
echo    RESULTATS DU TEST
echo ========================================
echo.
echo Si "Localhost OK" : Le serveur fonctionne
echo Si "IP Locale OK" : React Native peut se connecter
echo.
echo URL pour React Native: http://%LOCAL_IP%:4000
echo URL pour navigateur: http://localhost:4000
echo.
echo Si les deux tests echouent:
echo 1. Verifiez que Node.js est installe
echo 2. Verifiez que le port 4000 est libre
echo 3. Desactivez temporairement le firewall
echo.
pause
